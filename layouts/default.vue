<template>
    <v-app>
        <v-app-bar
            v-if="
                !['/login', '/login/', '/loginRedirect'].includes($route.path)
            "
            app
            dense
            fixed
        >
            <v-avatar size="32" tile>
                <img alt="logo" src="/oa_logo.png" />
            </v-avatar>

            <v-btn plain small text to="/"> 首页</v-btn>

            <v-menu bottom open-on-hover>
                <template v-slot:activator="{ on, attrs }">
                    <v-btn small text v-bind="attrs" v-on="on"> 工作中心</v-btn>
                </template>
                <v-list dense>
                    <v-list-item to="/processCenter">
                        <v-list-item-title> 流程中心</v-list-item-title>
                    </v-list-item>
                    <ProcessListButton />
                </v-list>
            </v-menu>

            <v-menu bottom open-on-hover>
                <template v-slot:activator="{ on, attrs }">
                    <v-btn small text v-bind="attrs" v-on="on"> 通知管理</v-btn>
                </template>
                <v-list dense>
                    <v-list-item to="/notificationList">
                        <v-list-item-title> 通知列表</v-list-item-title>
                    </v-list-item>
                    <NotificationCreate />
                </v-list>
            </v-menu>

            <v-spacer></v-spacer>
            <NotificationList />
            <UserAvatar />
        </v-app-bar>

        <v-main style="background-color: #f2f4f8">
            <Nuxt />
        </v-main>
    </v-app>
</template>

<script>
import UserAvatar from '../components/navBar/UserAvatar.vue';
import NotificationList from '../components/navBar/NotificationList.vue';
import ProcessListButton from '../components/adminRouteMenu/ProcessListButton.vue';
import NotificationCreate from '../components/adminRouteMenu/NotificationCreate.vue';

export default {
    name: 'DefaultLayout',
    components: {
        UserAvatar,
        NotificationList,
        ProcessListButton,
        NotificationCreate,
    },
};
</script>
