{"compilerOptions": {"target": "ES2018", "module": "ESNext", "moduleResolution": "Node", "lib": ["ESNext", "ESNext.AsyncIterable", "DOM"], "esModuleInterop": true, "allowJs": true, "sourceMap": true, "strict": true, "noEmit": true, "experimentalDecorators": true, "baseUrl": ".", "paths": {"~/*": ["./*"], "@/*": ["./*"]}, "types": ["@nuxt/types", "@types/node"]}, "exclude": ["node_modules", ".nuxt", "dist"]}