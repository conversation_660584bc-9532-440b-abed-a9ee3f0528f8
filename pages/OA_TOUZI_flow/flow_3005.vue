<template>
    <v-container fluid>
        <v-row>
            <v-col
                cols="12"
                lg="8"
                style="margin: 0 auto; max-width: 960px"
                xs="12"
            >
                <v-card class="pa-4">
                    <v-card-title>
                        <v-btn
                            color="primary"
                            icon
                            small
                            @click="$router.go(-1)"
                        >
                            <v-icon>mdi-arrow-left</v-icon>
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                            v-print="'#printForm'"
                            color="primary"
                            icon
                            small
                        >
                            <v-icon>mdi-printer</v-icon>
                        </v-btn>
                    </v-card-title>
                    <v-card-text>
                        <form
                            id="printForm"
                            style="
                                line-height: 32px;
                                color: black;
                                border: 1px solid black;
                                text-align: center;
                                margin-left: 1px;
                            "
                        >
                            <h1>
                                保利(天津)股权投资基金管理有限公司费用报销单
                            </h1>

                            <div style="text-align: right; margin-right: 16px">
                                日期:<span>{{ formData.date }}</span>
                            </div>

                            <div style="display: flex">
                                <div
                                    style="
                                        display: flex;
                                        flex-wrap: wrap;
                                        border-top: 1px solid black;
                                        flex: 3;
                                    "
                                >
                                    <div class="borderR">部门</div>
                                    <div class="borderR">
                                        <span>{{ formData.department }}</span>
                                    </div>
                                    <div class="borderR">经办人</div>
                                    <div class="borderR">
                                        <span>{{ formData.promoter }}</span>
                                    </div>

                                    <div class="flexWrap"></div>

                                    <div class="borderR">项目</div>
                                    <div
                                        class="borderR"
                                        style="
                                            display: flex;
                                            flex-wrap: wrap;
                                            flex: 3;
                                        "
                                    >
                                        <div class="borderR">金额</div>
                                        <div class="borderR">结算方式</div>
                                        <div class="borderR" style="border: 0">
                                            <span>{{
                                                formData.payMethod
                                            }}</span>
                                        </div>

                                        <div class="flexWrap"></div>

                                        <AmountUnit :NumberProps="units" />

                                        <div
                                            class="borderR"
                                            style="border: 0; flex: 2"
                                        >
                                            汇款信息/备注
                                        </div>
                                    </div>

                                    <div class="flexWrap"></div>

                                    <div style="display: flex; width: 100%">
                                        <div
                                            style="
                                                flex: 1;
                                                display: flex;
                                                flex-wrap: wrap;
                                            "
                                        >
                                            <div
                                                class="borderR"
                                                style="flex: 1"
                                            >
                                                {{
                                                    formData.amount[0]?.project
                                                }}
                                            </div>
                                            <div
                                                class="flexContainer"
                                                style="flex: 1"
                                            >
                                                <AmountUnit
                                                    :NumberProps="
                                                        formData.amount[0]
                                                            ?.amount
                                                    "
                                                />
                                            </div>

                                            <div class="flexWrap"></div>

                                            <div
                                                class="borderR"
                                                style="flex: 1"
                                            >
                                                {{
                                                    formData.amount[1]?.project
                                                }}
                                            </div>
                                            <div
                                                class="flexContainer"
                                                style="flex: 1"
                                            >
                                                <AmountUnit
                                                    :NumberProps="
                                                        formData.amount[1]
                                                            ?.amount
                                                    "
                                                />
                                            </div>

                                            <div class="flexWrap"></div>

                                            <div
                                                class="borderR"
                                                style="flex: 1"
                                            >
                                                {{
                                                    formData.amount[2]?.project
                                                }}
                                            </div>
                                            <div
                                                class="flexContainer"
                                                style="flex: 1"
                                            >
                                                <AmountUnit
                                                    :NumberProps="
                                                        formData.amount[2]
                                                            ?.amount
                                                    "
                                                />
                                            </div>

                                            <div class="flexWrap"></div>

                                            <div
                                                class="borderR"
                                                style="flex: 1"
                                            >
                                                {{
                                                    formData.amount[3]?.project
                                                }}
                                            </div>
                                            <div
                                                class="flexContainer"
                                                style="flex: 1"
                                            >
                                                <AmountUnit
                                                    :NumberProps="
                                                        formData.amount[3]
                                                            ?.amount
                                                    "
                                                />
                                            </div>

                                            <div class="flexWrap"></div>

                                            <div
                                                class="borderR"
                                                style="flex: 1"
                                            >
                                                {{
                                                    formData.amount[4]?.project
                                                }}
                                            </div>
                                            <div
                                                class="flexContainer"
                                                style="flex: 1"
                                            >
                                                <AmountUnit
                                                    :NumberProps="
                                                        formData.amount[4]
                                                            ?.amount
                                                    "
                                                />
                                            </div>

                                            <div class="flexWrap"></div>

                                            <div
                                                class="borderR"
                                                style="flex: 1"
                                            >
                                                合计
                                            </div>
                                            <div
                                                class="flexContainer"
                                                style="flex: 1"
                                            >
                                                <AmountUnit
                                                    :NumberProps="
                                                        formData.total
                                                    "
                                                />
                                            </div>
                                        </div>
                                        <div class="borderR">
                                            <span>{{ formData.note }}</span>
                                        </div>
                                    </div>

                                    <div class="flexWrap"></div>

                                    <div class="borderR">报销合计(大写)</div>
                                    <div class="borderR" style="flex: 3">
                                        <span>{{ formData.totalAmount }}</span>
                                    </div>

                                    <div class="flexWrap"></div>

                                    <div class="borderR">已借款金额</div>
                                    <div class="borderR">
                                        <span>{{ formData.borrowed }}</span>
                                    </div>
                                    <div class="borderR">交回/补付金额</div>
                                    <div class="borderR">
                                        <span>{{ formData.backPayment }}</span>
                                    </div>

                                    <div class="flexWrap"></div>

                                    <div class="borderR">票据张数</div>
                                    <div class="borderR">
                                        <span>{{ formData.billNumber }}</span>
                                    </div>
                                    <div class="borderR">交/领款人</div>
                                    <div class="borderR">
                                        <span>{{ formData.payee }}</span>
                                    </div>

                                    <div class="flexWrap"></div>

                                    <div class="borderR">审核</div>
                                    <div class="borderR">
                                        <span>{{ formData.auditing }}</span>
                                    </div>
                                    <div class="borderR">出纳</div>
                                    <div class="borderR">
                                        <span>{{ formData.cashier }}</span>
                                    </div>
                                </div>
                                <div
                                    style="
                                        flex: 1;
                                        display: flex;
                                        flex-direction: column;
                                    "
                                >
                                    <div class="borderT" style="flex: 1">
                                        董事长审批意见
                                    </div>
                                    <div class="borderT" style="flex: 2">
                                        <div
                                            v-for="item in formData.president"
                                            :key="item.index"
                                        >
                                            <span
                                                >{{ item.name }}:{{
                                                    item.content
                                                }}</span
                                            >
                                        </div>
                                    </div>
                                    <div class="borderT" style="flex: 1">
                                        总经理审批意见
                                    </div>
                                    <div class="borderT" style="flex: 2">
                                        <div
                                            v-for="item in formData.generalManager"
                                            :key="item.index"
                                        >
                                            <span
                                                >{{ item.name }}:{{
                                                    item.content
                                                }}</span
                                            >
                                        </div>
                                    </div>
                                    <div class="borderT" style="flex: 1">
                                        总会计师审批意见
                                    </div>
                                    <div class="borderT" style="flex: 2">
                                        <div
                                            v-for="item in formData.chiefAccountant"
                                            :key="item.index"
                                        >
                                            <span
                                                >{{ item.name }}:{{
                                                    item.content
                                                }}</span
                                            >
                                        </div>
                                    </div>
                                    <div class="borderT" style="flex: 1">
                                        经办部门审批意见
                                    </div>
                                    <div class="borderT" style="flex: 2">
                                        <div
                                            v-for="item in formData.mainOrganizers"
                                            :key="item.index"
                                        >
                                            <span
                                                >{{ item.name }}:{{
                                                    item.content
                                                }}</span
                                            >
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </v-card-text>
                </v-card>
            </v-col>

            <v-col cols="12" lg="4" xs="12">
                <v-card class="pa-4">
                    <v-tabs>
                        <v-tab @click="isShowTimeLine = false">内容</v-tab>
                        <v-tab @click="isShowTimeLine = true">时间线</v-tab>
                    </v-tabs>
                    <v-card-text v-show="!isShowTimeLine">
                        <v-form v-if="jobInfo.isCreating">
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.date"
                                        :rules="[rules.required]"
                                        dense
                                        label="日期"
                                    />
                                </v-col>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.department"
                                        :rules="[rules.required]"
                                        dense
                                        label="部门"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.payMethod"
                                        :rules="[rules.required]"
                                        dense
                                        label="结算方式"
                                    />
                                </v-col>

                                <v-col>
                                    <v-text-field
                                        v-model="formData.note"
                                        dense
                                        label="汇款信息/备注"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.amount[0].project"
                                        dense
                                        label="项目"
                                    />
                                </v-col>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.amount[0].amount"
                                        dense
                                        label="金额 例: 23.00"
                                        type="number"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.amount[1].project"
                                        dense
                                        label="项目"
                                    />
                                </v-col>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.amount[1].amount"
                                        dense
                                        label="金额 例: 23.00"
                                        type="number"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.amount[2].project"
                                        dense
                                        label="项目"
                                    />
                                </v-col>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.amount[2].amount"
                                        dense
                                        label="金额 例: 23.00"
                                        type="number"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.amount[3].project"
                                        dense
                                        label="项目"
                                    />
                                </v-col>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.amount[3].amount"
                                        dense
                                        label="金额 例: 23.00"
                                        type="number"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.amount[4].project"
                                        dense
                                        label="项目"
                                    />
                                </v-col>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.amount[4].amount"
                                        dense
                                        label="金额 例: 23.00"
                                        type="number"
                                    />
                                </v-col>
                            </v-row>

                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.borrowed"
                                        :rules="[rules.required]"
                                        dense
                                        label="已借款金额"
                                    />
                                </v-col>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.backPayment"
                                        :rules="[rules.required]"
                                        dense
                                        label="交回/补付金额"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.billNumber"
                                        :rules="[rules.required]"
                                        dense
                                        label="票据张数"
                                    />
                                </v-col>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.payee"
                                        :rules="[rules.required]"
                                        dense
                                        label="交/领款人"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.auditing"
                                        :rules="[rules.required]"
                                        dense
                                        label="审核"
                                    />
                                </v-col>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.cashier"
                                        :rules="[rules.required]"
                                        dense
                                        label="出纳"
                                    />
                                </v-col>
                            </v-row>
                        </v-form>
                        <v-form
                            v-if="
                                isEditable &&
                                (jobInfo.isReviewing || jobInfo.isSigning) &&
                                !jobInfo.isEnding
                            "
                        >
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="currentComment"
                                        :rules="[rules.required]"
                                        dense
                                        label="请输入批示"
                                    />
                                </v-col>
                            </v-row>
                            <ReviewChips :getChipValue="getChipValue" />
                        </v-form>
                        <v-row>
                            <v-col>
                                正文:
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.mainFiles"
                                    :getFileInfo="getMainFileInfo"
                                />
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                附件:
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.attachFiles"
                                    :getFileInfo="getAttachFileInfo"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>

                    <ActionButtons
                        v-if="isShowFunctionArea"
                        :currentComment="currentComment"
                        :currentCommentField="currentCommentField"
                        :formInfo="formInfo"
                        :isEditable="isEditable"
                        :isShowTimeLine="isShowTimeLine"
                        :jobInfo="jobInfo"
                        :nodeInfo="nodeInfo"
                        :workflowId="workflowId"
                    />
                    <v-card-text v-if="isShowTimeLine">
                        <TimelineCard
                            :jobList="jobInfo.jobList"
                            :nodeList="nodeInfo.nodeList"
                        />
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import print from 'vue-print-nb';
import FileUploadV1 from '../../components/FileUploadV1.vue';
import TimelineCard from '../../components/formFlow/TimelineCard.vue';
import { localUserInfo } from '../../helper/localUserInfo';

import initForm from '../../helper/initForm';
import isEditable from '../../helper/isEditable';

import ReviewChips from '../../components/formFlow/formComponent/ReviewChips.vue';
import ActionButtons from '../../components/formFlow/ActionButtons.vue';

import AmountUnit from '../../components/formFlow/formComponent/AmountUnit.vue';

export default {
    name: 'flow_3005',
    directives: { print },
    components: {
        AmountUnit,
        ActionButtons,
        ReviewChips,
        FileUploadV1,
        TimelineCard,
    },
    data() {
        return {
            units: [
                '分',
                '角',
                '.',
                '元',
                '十',
                '百',
                '千',
                '万',
                '十万',
                '百万',
            ],

            isShowFunctionArea: false,
            isEditable: false,
            isShowTimeLine: false,

            jobId: '',
            nodeId: '',
            workflowId: '',

            formInfo: {},
            jobInfo: {},
            nodeInfo: {},

            currentComment: '',
            currentCommentField: '',

            formData: {
                title: '',

                date: new Date(Date.now())
                    .toLocaleDateString('zh-CN')
                    .replace(/\//g, '-'),

                department: '',
                promoter: '',
                payMethod: '',
                amount: [
                    { project: '', amount: '' },
                    { project: '', amount: '' },
                    { project: '', amount: '' },
                    { project: '', amount: '' },
                    { project: '', amount: '' },
                ],
                note: '',
                total: '',
                totalAmount: '',
                borrowed: '',
                backPayment: '',
                billNumber: '',
                payee: '',
                auditing: '',
                cashier: '',

                president: [],
                generalManager: [],
                chiefAccountant: [],
                mainOrganizers: [],

                mainFiles: '',
                attachFiles: '',
            },

            userId: localUserInfo.id,
            userName: localUserInfo.fullName,

            rules: {
                required: (value) => !!value || '此条目不能为空',
            },
        };
    },
    methods: {
        getMainFileInfo(filesList) {
            this.formData.mainFiles = filesList;
        },

        getAttachFileInfo(filesList) {
            this.formData.attachFiles = filesList;
        },

        getChipValue(chip) {
            this.currentComment = chip;
        },

        async initHtmlForm() {
            if (this.$route.query.workflowId) {
                this.jobId = this.$route.query.jobId;
                this.nodeId = this.$route.query.nodeId;
                this.workflowId = this.$route.query.workflowId;

                const initInfo = await initForm(
                    this.$axios,
                    this.jobId,
                    this.nodeId,
                    this.workflowId
                );

                this.formInfo = initInfo.formInfo;
                this.jobInfo = initInfo.jobInfo;
                this.nodeInfo = initInfo.nodeInfo;
                this.formData = initInfo.formInfo.formData;
                this.reviewUserList = this.formData.reviewUserList;

                this.isEditable = isEditable(this.jobInfo.jobList, this.userId);
                this.isShowFunctionArea = true;

                if (!this.isEditable) return;
                if (this.jobInfo.isEnding) return;
                if (this.jobInfo.isCreating) return;

                switch (this.nodeInfo.nodeTitle) {
                    case '董事长':
                        this.currentCommentField = 'president';

                        break;
                    case '总经理':
                        this.currentCommentField = 'generalManager';

                        break;
                    case '总会计师':
                        this.currentCommentField = 'chiefAccountant';

                        break;
                    default:
                        this.currentCommentField = 'mainOrganizers';

                        break;
                }

                if (!this.jobInfo.isStaging) {
                    this.formData[this.currentCommentField].unshift({
                        name: this.userName,
                        content: '',
                    });
                }
            } else {
                this.formData.title = `${this.userName}的报销申请`;
                this.formData.promoter = this.userName;
                this.formInfo.formData = this.formData;
                this.jobInfo.isCreating = true;
                this.isEditable = true;
                this.isShowFunctionArea = true;
            }
        },
    },

    mounted() {
        this.initHtmlForm();
    },

    watch: {
        currentComment(newValue) {
            this.formData[this.currentCommentField][0].content = newValue;
        },
    },
};
</script>

<style scoped>
* {
    box-sizing: border-box;
}

span {
    word-wrap: anywhere;
}

.borderR {
    border-right: 1px solid black;
    flex: 1;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.borderT {
    border-top: 1px solid black;
}

.flexContainer {
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.flexWrap {
    width: 100%;
    height: 1px;
    background-color: transparent;
    border-bottom: 1px solid black;
}
</style>
