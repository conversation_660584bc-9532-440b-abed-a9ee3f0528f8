<template>
    <v-container fluid>
        <v-row>
            <v-col
                cols="12"
                lg="8"
                style="margin: 0 auto; max-width: 960px"
                xs="12"
            >
                <v-card class="pa-4">
                    <v-card-title>
                        <v-btn
                            color="primary"
                            icon
                            small
                            @click="$router.go(-1)"
                        >
                            <v-icon>mdi-arrow-left</v-icon>
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                            v-print="'#printForm'"
                            color="primary"
                            icon
                            small
                        >
                            <v-icon>mdi-printer</v-icon>
                        </v-btn>
                    </v-card-title>
                    <v-card-text>
                        <form
                            id="printForm"
                            style="
                                color: black;
                                line-height: 24px;
                                display: grid;
                                grid-template-columns: 1fr;
                                gap: 1px;
                                background-color: black;
                                border: 1px solid black;
                            "
                        >
                            <div>
                                <h1
                                    style="
                                        text-align: center;
                                        min-height: 64px;
                                        line-height: 64px;
                                    "
                                >
                                    发&nbsp;&nbsp;&nbsp;&nbsp;文
                                </h1>
                            </div>

                            <div style="min-height: 96px">
                                标题:
                                <div>{{ formData.title }}</div>
                            </div>

                            <div style="min-height: 94px">
                                备注:
                                <div>{{ formData.note }}</div>
                            </div>

                            <div style="min-height: 96px; display: flex">
                                <div style="flex: 1">
                                    发文日期:
                                    <div>{{ formData.date }}</div>
                                </div>
                                <div
                                    style="
                                        flex: 1;
                                        border-left: 1px solid black;
                                    "
                                >
                                    文号:
                                    <div>{{ formData.docNumber }}</div>
                                </div>
                            </div>

                            <div style="min-height: 96px">
                                <div>校对签字:</div>
                                <div
                                    v-for="item in formData.signature"
                                    :key="item.index"
                                >
                                    <span
                                        >{{ item.name }} :
                                        {{ item.content }}</span
                                    >
                                </div>
                            </div>
                        </form>
                    </v-card-text>
                </v-card>
            </v-col>
            <v-col cols="12" lg="4" xs="12">
                <v-card class="pa-4">
                    <v-card-title>
                        <v-tabs>
                            <v-tab @click="isShowTimeLine = false">内容</v-tab>
                            <v-tab @click="isShowTimeLine = true">时间线</v-tab>
                        </v-tabs>
                    </v-card-title>
                    <v-card-text v-show="!isShowTimeLine">
                        <v-form v-if="jobInfo.isCreating">
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.title"
                                        :rules="[rules.required]"
                                        dense
                                        label="标题"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.note"
                                        dense
                                        label="备注"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-menu
                                        ref="date_picker_menu"
                                        v-model="datePickerMenu"
                                        :close-on-content-click="false"
                                        :return-value.sync="formData.date"
                                        min-width="auto"
                                        offset-y
                                        transition="scale-transition"
                                    >
                                        <template
                                            v-slot:activator="{ on, attrs }"
                                        >
                                            <v-text-field
                                                v-model="formData.date"
                                                prepend-icon="mdi-calendar"
                                                readonly
                                                v-bind="attrs"
                                                v-on="on"
                                            ></v-text-field>
                                        </template>
                                        <v-date-picker
                                            v-model="formData.date"
                                            locale="zh-cn"
                                            no-title
                                        >
                                            <v-spacer></v-spacer>
                                            <v-btn
                                                color="primary"
                                                text
                                                @click="datePickerMenu = false"
                                            >
                                                取消
                                            </v-btn>
                                            <v-btn
                                                color="primary"
                                                text
                                                @click="
                                                    $refs.date_picker_menu.save(
                                                        formData.date
                                                    )
                                                "
                                            >
                                                确定
                                            </v-btn>
                                        </v-date-picker>
                                    </v-menu>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.docNumber"
                                        :rules="[rules.required]"
                                        dense
                                        label="文号"
                                    />
                                </v-col>
                            </v-row>
                        </v-form>
                        <v-form
                            v-if="
                                isEditable &&
                                (jobInfo.isReviewing || jobInfo.isSigning) &&
                                !jobInfo.isEnding
                            "
                        >
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="currentComment"
                                        :rules="[rules.required]"
                                        dense
                                        label="请输入批示"
                                    />
                                </v-col>
                            </v-row>
                            <ReviewChips :getChipValue="getChipValue" />
                        </v-form>
                        <v-row>
                            <v-col>
                                正文:
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.mainFiles"
                                    :getFileInfo="getMainFileInfo"
                                />
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                附件:
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.attachFiles"
                                    :getFileInfo="getAttachFileInfo"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>
                    <ActionButtons
                        v-if="isShowFunctionArea"
                        :currentComment="currentComment"
                        :currentCommentField="currentCommentField"
                        :formInfo="formInfo"
                        :isEditable="isEditable"
                        :isShowTimeLine="isShowTimeLine"
                        :jobInfo="jobInfo"
                        :nodeInfo="nodeInfo"
                        :workflowId="workflowId"
                    />

                    <v-card-text v-if="isShowTimeLine">
                        <TimelineCard
                            :jobList="jobInfo.jobList"
                            :nodeList="nodeInfo.nodeList"
                        />
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import print from 'vue-print-nb';
import FileUploadV1 from '../../components/FileUploadV1.vue';
import TimelineCard from '../../components/formFlow/TimelineCard.vue';
import { localUserInfo } from '../../helper/localUserInfo';

import initForm from '../../helper/initForm';
import isEditable from '../../helper/isEditable';

import ReviewChips from '../../components/formFlow/formComponent/ReviewChips.vue';
import ActionButtons from '../../components/formFlow/ActionButtons.vue';

export default {
    name: 'flow_3013',
    directives: { print },
    components: {
        ActionButtons,
        ReviewChips,
        FileUploadV1,
        TimelineCard,
    },
    data() {
        return {
            isShowFunctionArea: false,
            isEditable: false,
            isShowTimeLine: false,

            jobId: '',
            nodeId: '',
            workflowId: '',

            jobInfo: {},
            formInfo: {},
            nodeInfo: {},

            currentComment: '',
            currentCommentField: '',

            datePickerMenu: false,

            formData: {
                title: '',
                note: '',
                date: new Date(Date.now())
                    .toLocaleDateString('zh-CN')
                    .replace(/\//g, '-'),
                docNumber: '',
                signature: [],

                mainFiles: '',
                attachFiles: '',
            },

            rules: {
                required: (value) => !!value || '此条目不能为空',
            },

            userId: localUserInfo.id,
            userName: localUserInfo.fullName,
        };
    },
    methods: {
        getMainFileInfo(filesList) {
            this.formData.mainFiles = filesList;
        },
        getAttachFileInfo(filesList) {
            this.formData.attachFiles = filesList;
        },
        getChipValue(chip) {
            this.currentComment = chip;
        },

        async initHtmlForm() {
            if (this.$route.query.workflowId) {
                this.jobId = this.$route.query.jobId;
                this.nodeId = this.$route.query.nodeId;
                this.workflowId = this.$route.query.workflowId;

                const initInfo = await initForm(
                    this.$axios,
                    this.jobId,
                    this.nodeId,
                    this.workflowId
                );

                this.jobInfo = initInfo.jobInfo;
                this.formInfo = initInfo.formInfo;
                this.nodeInfo = initInfo.nodeInfo;
                this.formData = initInfo.formInfo.formData;

                this.isEditable = isEditable(this.jobInfo.jobList, this.userId);
                this.isShowFunctionArea = true;

                if (!this.isEditable) return;
                if (this.jobInfo.isEnding) return;
                if (this.jobInfo.isCreating) return;

                this.currentCommentField = 'signature';

                if (!this.jobInfo.isStaging) {
                    this.formData[this.currentCommentField].unshift({
                        name: this.userName,
                        content: '',
                    });
                }

                return;
            }

            this.formInfo.formData = this.formData;
            this.jobInfo.isCreating = true;

            this.isEditable = true;
            this.isShowFunctionArea = true;
        },
    },
    mounted() {
        this.initHtmlForm();
    },
    watch: {
        currentComment(newValue) {
            this.formData[this.currentCommentField][0].content = newValue;
        },
    },
};
</script>

<style scoped>
span {
    color: black;
    word-wrap: anywhere;
}

div {
    background-color: white;
}
</style>
