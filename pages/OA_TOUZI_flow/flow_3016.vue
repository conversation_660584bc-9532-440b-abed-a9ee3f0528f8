<template>
    <v-container fluid>
        <v-row>
            <v-col
                cols="12"
                lg="8"
                style="margin: 0 auto; max-width: 960px"
                xs="12"
            >
                <v-card class="pa-4">
                    <v-card-title>
                        <v-btn
                            color="primary"
                            icon
                            small
                            @click="$router.go(-1)"
                        >
                            <v-icon>mdi-arrow-left</v-icon>
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                            v-print="'#printForm'"
                            color="primary"
                            icon
                            small
                        >
                            <v-icon>mdi-printer</v-icon>
                        </v-btn>
                    </v-card-title>
                    <v-card-text>
                        <form
                            id="printForm"
                            style="
                                color: red;
                                line-height: 24px;
                                display: grid;
                                grid-template-columns: 1fr;
                                gap: 1px;
                                background: red;
                            "
                        >
                            <div>
                                <h1
                                    style="
                                        text-align: center;
                                        margin-bottom: 24px;
                                    "
                                >
                                    呈&nbsp;&nbsp;&nbsp;&nbsp;批
                                    &nbsp;&nbsp;&nbsp;&nbsp;件
                                </h1>
                                <div>
                                    保利（北京）私募基金管理有限公司
                                    <div style="float: right">
                                        [<span>{{ formData.docType }}</span
                                        >]呈&nbsp;<span>{{
                                            formData.docDep
                                        }}</span
                                        >&nbsp;字&nbsp;第&nbsp;<span>{{
                                            formData.docNumber
                                        }}</span
                                        >&nbsp;号
                                    </div>
                                </div>
                            </div>

                            <div>
                                标题:
                                <span>{{ formData.title }}</span>
                            </div>

                            <div
                                v-for="user in reviewUserList"
                                :key="user.uid"
                                style="min-height: 64px"
                            >
                                <div>{{ user.name }}批示:</div>
                                <div
                                    v-for="comment in formData[user.pinyin]"
                                    :key="comment.index"
                                >
                                    <span>{{ comment.content }}</span>
                                </div>
                            </div>

                            <div style="min-height: 64px">
                                <div>主办部门:</div>
                                <div
                                    v-for="item in formData.mainOrganizers"
                                    :key="item.index"
                                >
                                    <span
                                        >{{ item.name }}
                                        {{ item.content }}</span
                                    >
                                </div>
                            </div>

                            <div style="min-height: 64px">
                                <div>会签部门:</div>
                                <div
                                    v-for="item in formData.countersignDepartment"
                                    :key="item.index"
                                >
                                    <span
                                        >{{ item.name }} :
                                        {{ item.content }}</span
                                    >
                                </div>
                            </div>

                            <div style="display: flex">
                                <div style="flex: 1">
                                    承办人:
                                    <span>{{ formData.promoter }}</span>
                                </div>
                                <div style="flex: 1">
                                    电话:
                                    <span>{{ formData.tel }}</span>
                                </div>
                            </div>
                        </form>
                    </v-card-text>
                </v-card>
            </v-col>
            <v-col cols="12" lg="4" xs="12">
                <v-card class="pa-4">
                    <v-card-title>
                        <v-tabs>
                            <v-tab @click="isShowTimeLine = false">内容</v-tab>
                            <v-tab @click="isShowTimeLine = true">时间线</v-tab>
                        </v-tabs>
                    </v-card-title>
                    <v-card-text v-show="!isShowTimeLine">
                        <v-form v-if="jobInfo.isCreating">
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.docType"
                                        :rules="[rules.required]"
                                        dense
                                        label="呈"
                                    />
                                </v-col>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.docDep"
                                        :rules="[rules.required]"
                                        dense
                                        label="字"
                                    />
                                </v-col>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.docNumber"
                                        :rules="[rules.required]"
                                        dense
                                        label="号"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.title"
                                        :rules="[rules.required]"
                                        dense
                                        label="标题"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-select
                                        v-model="formData.docCategory"
                                        :items="[
                                            {
                                                text: '规章制度',
                                                value: '规章制度',
                                            },
                                            {
                                                text: '经济合同',
                                                value: '经济合同',
                                            },
                                            {
                                                text: '经营管理事项',
                                                value: '经营管理事项',
                                            },
                                            {
                                                text: '以上都不涉及',
                                                value: '以上都不涉及',
                                            },
                                        ]"
                                        :rules="[rules.vSelect]"
                                        dense
                                        label="审批文件涉及内容"
                                        multiple
                                        @input="computeDocCategory"
                                    />
                                </v-col>
                            </v-row>
                        </v-form>
                        <v-form
                            v-if="
                                isEditable &&
                                (jobInfo.isReviewing || jobInfo.isSigning) &&
                                !jobInfo.isEnding
                            "
                        >
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="currentComment"
                                        :rules="[rules.required]"
                                        dense
                                        label="请输入批示"
                                    />
                                </v-col>
                            </v-row>
                            <ReviewChips :getChipValue="getChipValue" />
                        </v-form>
                        <v-row v-if="isDisplayDocCategory()">
                            <v-col>
                                <div style="font-size: 16px; color: black">
                                    {{ formData.docCategory.join(',') }}
                                </div>
                                <br />
                                <div style="font-size: 16px; color: red">
                                    <v-icon color="red" size="16"
                                        >mdi-alert
                                    </v-icon>
                                    姬玮莉 待会签
                                </div>
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                正文:
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.mainFiles"
                                    :getFileInfo="getMainFileInfo"
                                />
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                附件:
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.attachFiles"
                                    :getFileInfo="getAttachFileInfo"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>
                    <ActionButtons
                        v-if="isShowFunctionArea"
                        :currentComment="currentComment"
                        :currentCommentField="currentCommentField"
                        :formInfo="formInfo"
                        :isEditable="isEditable"
                        :isShowTimeLine="isShowTimeLine"
                        :jobInfo="jobInfo"
                        :nodeInfo="nodeInfo"
                        :workflowId="workflowId"
                    />
                    <v-card-text v-if="isShowTimeLine">
                        <TimelineCard
                            :jobList="jobInfo.jobList"
                            :nodeList="nodeInfo.nodeList"
                        />
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import print from 'vue-print-nb';
import FileUploadV1 from '../../components/FileUploadV1.vue';
import TimelineCard from '../../components/formFlow/TimelineCard.vue';
import { localUserInfo } from '../../helper/localUserInfo';

import initForm from '../../helper/initForm';
import isEditable from '../../helper/isEditable';

import ReviewChips from '../../components/formFlow/formComponent/ReviewChips.vue';
import ActionButtons from '../../components/formFlow/ActionButtons.vue';

export default {
    name: 'flow_3001',
    directives: { print },
    components: {
        ActionButtons,
        ReviewChips,
        FileUploadV1,
        TimelineCard,
    },
    data() {
        return {
            isShowFunctionArea: false,
            isEditable: false,
            isShowTimeLine: false,

            jobId: '',
            nodeId: '',
            workflowId: '',

            jobInfo: {},
            formInfo: {},
            nodeInfo: {},

            currentComment: '',
            currentCommentField: '',

            formData: {
                docType: '',
                docNumber: '',
                docDep: '',
                docCategory: [],

                title: '',
                promoter: '',
                tel: '',

                HJ: [],
                ZPL: [],
                LYM: [],
                JWL: [],

                mainOrganizers: [],
                countersignDepartment: [],

                mainFiles: '',
                attachFiles: '',

                reviewUserList: [],
            },

            reviewUserList: [
                {
                    uid: 'd5999392-48be-11eb-8f84-7b9620caa627',
                    name: '何军（董事长）',
                    pinyin: 'HJ',
                },
                {
                    uid: 'fe064ee0-64c3-11ed-84e3-5bbd2b44807b',
                    name: '赵鹏利（总经理）',
                    pinyin: 'ZPL',
                },
                {
                    uid: '71d4b970-40dd-11ed-887e-a1fba4893fe6',
                    name: '刘一敏（总会计师）',
                    pinyin: 'LYM',
                },
                {
                    uid: 'fe0e3e20-64c3-11ed-84e3-5bbd2b44807b',
                    name: '姬玮莉（总法律顾问暨首席合规官）',
                    pinyin: 'JWL',
                },
            ],

            userId: localUserInfo.id,
            userName: localUserInfo.fullName,

            rules: {
                required: (value) => !!value || '此条目不能为空',
                vSelect: (value) => value.length > 0 || '至少选择一个选项',
            },
        };
    },
    methods: {
        isDisplayDocCategory() {
            if (
                !this.formData.docCategory ||
                this.formData.docCategory.length === 0 ||
                this.formData.JWL.length > 0
            )
                return false;
            return !this.formData.docCategory.includes('以上都不涉及');
        },
        computeDocCategory(value) {
            if (value.includes('以上都不涉及')) {
                this.formData.docCategory = ['以上都不涉及'];
            } else {
                this.formData.docCategory = value;
            }
        },

        getMainFileInfo(filesList) {
            this.formData.mainFiles = filesList;
        },

        getAttachFileInfo(filesList) {
            this.formData.attachFiles = filesList;
        },

        getChipValue(chip) {
            this.currentComment = chip;
        },

        async initHtmlForm() {
            if (this.$route.query.workflowId) {
                this.jobId = this.$route.query.jobId;
                this.nodeId = this.$route.query.nodeId;
                this.workflowId = this.$route.query.workflowId;

                const initInfo = await initForm(
                    this.$axios,
                    this.jobId,
                    this.nodeId,
                    this.workflowId
                );

                this.jobInfo = initInfo.jobInfo;
                this.formInfo = initInfo.formInfo;
                this.nodeInfo = initInfo.nodeInfo;
                this.formData = initInfo.formInfo.formData;
                this.reviewUserList = this.formData.reviewUserList;

                this.isEditable = isEditable(this.jobInfo.jobList, this.userId);
                this.isShowFunctionArea = true;

                if (!this.isEditable) return;
                if (this.jobInfo.isEnding) return;
                if (this.jobInfo.isCreating) return;

                const userOnForm = this.formData.reviewUserList.find(
                    (item) => item.uid === this.userId
                );

                if (userOnForm) {
                    this.currentCommentField = userOnForm.pinyin;
                } else {
                    this.currentCommentField = this.jobInfo.isSigning
                        ? 'countersignDepartment'
                        : 'mainOrganizers';
                }

                if (!this.jobInfo.isStaging) {
                    this.formData[this.currentCommentField].unshift({
                        name: this.userName,
                        content: '',
                    });
                }

                return;
            }

            this.formData.promoter = this.userName;
            this.formData.tel = localUserInfo.mobile;
            this.formData.reviewUserList = this.reviewUserList;

            this.formInfo.formData = this.formData;
            this.jobInfo.isCreating = true;

            this.isEditable = true;
            this.isShowFunctionArea = true;
        },
    },
    mounted() {
        this.initHtmlForm();
    },
    watch: {
        currentComment(newValue) {
            this.formData[this.currentCommentField][0].content = newValue;
        },
    },
};
</script>

<style scoped>
div {
    background-color: white;
}

span {
    color: black;
}
</style>
