<template>
    <v-container fluid>
        <v-row>
            <v-col
                cols="12"
                lg="8"
                style="margin: 0 auto; max-width: 960px"
                xs="12"
            >
                <v-card class="pa-4">
                    <v-card-title>
                        <v-btn
                            color="primary"
                            icon
                            small
                            @click="$router.go(-1)"
                        >
                            <v-icon>mdi-arrow-left</v-icon>
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                            v-print="'#printForm'"
                            color="primary"
                            icon
                            small
                        >
                            <v-icon>mdi-printer</v-icon>
                        </v-btn>
                    </v-card-title>
                    <v-card-text>
                        <form
                            id="printForm"
                            style="
                                line-height: 32px;
                                color: black;
                                border: 1px solid black;
                                text-align: center;
                            "
                        >
                            <h1>保利投资控股有限公司</h1>
                            <h1 class="borderB">请假条</h1>

                            <div style="display: flex; flex-wrap: wrap">
                                <div class="borderR">姓名</div>
                                <div class="borderR">
                                    <span>{{ formData.name }}</span>
                                </div>
                                <div class="borderR">部门</div>
                                <div style="flex: 1">
                                    <span>{{ formData.department }}</span>
                                </div>

                                <div class="flexWrap"></div>

                                <div class="borderR">参加工作时间</div>
                                <div class="borderR">
                                    <span>{{ formData.workTime }}</span>
                                </div>
                                <div class="borderR">职务</div>
                                <div style="flex: 1">
                                    <span>{{ formData.position }}</span>
                                </div>

                                <div class="flexWrap"></div>

                                <div style="display: flex; width: 100%">
                                    <div style="flex: 1; align-self: center">
                                        请假类别:
                                    </div>
                                    <div style="flex: 3">
                                        <v-radio-group
                                            v-model="formData.vacationType"
                                            :disabled="!isEditable"
                                            dense
                                            mandatory
                                            row
                                        >
                                            <v-radio
                                                v-for="item in leaveReason"
                                                :key="item.value"
                                                :label="item.text"
                                                :value="item.value"
                                            >
                                            </v-radio>
                                        </v-radio-group>
                                    </div>
                                </div>

                                <div class="flexWrap"></div>

                                <div style="display: flex; width: 100%">
                                    <div style="flex: 1; align-self: center">
                                        请假事由:
                                    </div>
                                    <div style="flex: 3">
                                        <span>{{
                                            formData.vacationReason
                                        }}</span>
                                    </div>
                                </div>

                                <div class="flexWrap"></div>

                                <div style="display: flex; width: 100%">
                                    <div style="flex: 1">请假时间:</div>
                                    <div style="flex: 3">
                                        自&nbsp;<span>{{
                                            formData.vacationDate
                                        }}</span
                                        >&nbsp;&nbsp;共计&nbsp;<span>{{
                                            formData.vacationDays
                                        }}</span
                                        >&nbsp;天
                                    </div>
                                </div>
                                <div class="flexWrap"></div>

                                <div style="display: flex; width: 100%">
                                    <div style="flex: 1; align-self: center">
                                        工作移交安排:
                                    </div>
                                    <div style="flex: 3">
                                        <span>{{
                                            formData.workHandoverArrangement
                                        }}</span>
                                    </div>
                                </div>

                                <div class="flexWrap"></div>

                                <div style="display: flex; width: 100%">
                                    <div style="flex: 1; align-self: center">
                                        工作接手人签字:
                                    </div>
                                    <div style="flex: 3">
                                        <span>{{
                                            formData.takingOverPersonSignature
                                        }}</span>
                                    </div>
                                </div>

                                <div class="flexWrap"></div>

                                <div class="borderR">批准人签字</div>
                                <div class="borderR">
                                    <span
                                        v-for="item in formData.approveSign"
                                        :key="item.index"
                                        >{{ item.name }}
                                        {{ item.content }}</span
                                    >
                                </div>
                                <div class="borderR">部门签字</div>
                                <div style="flex: 1">
                                    <span
                                        v-for="item in formData.departmentSign"
                                        :key="item.index"
                                        >{{ item.name }}
                                        {{ item.content }}</span
                                    >
                                </div>

                                <div class="flexWrap"></div>

                                <div class="borderR">综合人事部签字</div>
                                <div class="borderR">
                                    <span
                                        v-for="item in formData.originSign"
                                        :key="item.index"
                                        >{{ item.name }}
                                        {{ item.content }}</span
                                    >
                                </div>
                                <div class="borderR">请假人签字</div>
                                <div style="flex: 1">
                                    <span>{{ formData.promoterSign }}</span>
                                </div>

                                <div class="flexWrap"></div>

                                <div style="display: flex; width: 100%">
                                    <div style="flex: 1">日期:</div>
                                    <div style="flex: 3">
                                        <span>{{ formData.date }}</span>
                                    </div>
                                </div>

                                <div class="flexWrap"></div>

                                <div style="width: 100%; text-align: left">
                                    <p>备注:</p>
                                    <p>
                                        1.员工请探亲假、年休假、一周以上病假、工伤假、产假、哺乳假等须综合人事部对假期提
                                        出核定意见。
                                    </p>
                                    <p>
                                        2原则上员工休假一天以上需要提前做好工作移交安排,明确工作交接人,避免影响正常工作。
                                    </p>
                                    <p>
                                        3.员工年休假须事先请假,由所在部门领导审核报主管领导批准;部门领导休假由主管领导审批,
                                    </p>
                                    <p>
                                        报总经理备案;副总经理、三总师休假由总经理审批。根据工作情况,决定一次性或分期休假。其
                                        它假期审批权限见员工请休假制度。
                                    </p>
                                    <p>4.请假申请批准后交综合人事部保存。</p>
                                    <p>
                                        5.请假人一定要提前对工作接手人做好培训,并做好工作交接,确保其能够完成接受的工作。
                                    </p>
                                </div>
                            </div>
                        </form>
                    </v-card-text>
                </v-card>
            </v-col>
            <v-col cols="12" lg="4" xs="12">
                <v-card class="pa-4">
                    <v-tabs>
                        <v-tab @click="isShowTimeLine = false">内容</v-tab>
                        <v-tab @click="isShowTimeLine = true">时间线</v-tab>
                    </v-tabs>
                    <v-card-text v-show="!isShowTimeLine">
                        <v-form v-if="jobInfo.isCreating">
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.name"
                                        :rules="[rules.required]"
                                        dense
                                        label="姓名"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.department"
                                        :rules="[rules.required]"
                                        dense
                                        label="部门"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.workTime"
                                        :rules="[rules.required]"
                                        dense
                                        label="参加工作时间"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.position"
                                        :rules="[rules.required]"
                                        dense
                                        label="职务"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.vacationReason"
                                        :rules="[rules.required]"
                                        dense
                                        label="请假事由"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-menu
                                        ref="date_picker_menu"
                                        v-model="datePickerMenu"
                                        :close-on-content-click="false"
                                        :return-value.sync="
                                            formData.vacationDate
                                        "
                                        min-width="auto"
                                        offset-y
                                        transition="scale-transition"
                                    >
                                        <template
                                            v-slot:activator="{ on, attrs }"
                                        >
                                            <v-text-field
                                                v-model="formData.vacationDate"
                                                label="请假时间"
                                                prepend-icon="mdi-calendar"
                                                readonly
                                                v-bind="attrs"
                                                v-on="on"
                                            ></v-text-field>
                                        </template>
                                        <v-date-picker
                                            v-model="formData.vacationDate"
                                            locale="zh-cn"
                                            no-title
                                        >
                                            <v-spacer></v-spacer>
                                            <v-btn
                                                color="primary"
                                                text
                                                @click="datePickerMenu = false"
                                            >
                                                取消
                                            </v-btn>
                                            <v-btn
                                                color="primary"
                                                text
                                                @click="
                                                    $refs.date_picker_menu.save(
                                                        formData.vacationDate
                                                    )
                                                "
                                            >
                                                确定
                                            </v-btn>
                                        </v-date-picker>
                                    </v-menu>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.vacationDays"
                                        :rules="[rules.required]"
                                        dense
                                        label="请假时长"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="
                                            formData.workHandoverArrangement
                                        "
                                        :rules="[rules.required]"
                                        dense
                                        label="工作移交安排"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="
                                            formData.takingOverPersonSignature
                                        "
                                        :rules="[rules.required]"
                                        dense
                                        label="工作接手人签字"
                                    />
                                </v-col>
                            </v-row>
                        </v-form>
                        <v-form
                            v-if="
                                isEditable &&
                                (jobInfo.isReviewing || jobInfo.isSigning) &&
                                !jobInfo.isEnding
                            "
                        >
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="currentComment"
                                        :rules="[rules.required]"
                                        dense
                                        label="请输入批示"
                                    />
                                </v-col>
                            </v-row>
                            <ReviewChips :getChipValue="getChipValue" />
                        </v-form>
                        <v-row>
                            <v-col>
                                正文:
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.mainFiles"
                                    :getFileInfo="getMainFileInfo"
                                />
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                附件:
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.attachFiles"
                                    :getFileInfo="getAttachFileInfo"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>

                    <ActionButtons
                        v-if="isShowFunctionArea"
                        :currentComment="currentComment"
                        :currentCommentField="currentCommentField"
                        :formInfo="formInfo"
                        :isEditable="isEditable"
                        :isShowTimeLine="isShowTimeLine"
                        :jobInfo="jobInfo"
                        :nodeInfo="nodeInfo"
                        :workflowId="workflowId"
                    />
                    <v-card-text v-if="isShowTimeLine">
                        <TimelineCard
                            :jobList="jobInfo.jobList"
                            :nodeList="nodeInfo.nodeList"
                        />
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import print from 'vue-print-nb';
import FileUploadV1 from '../../components/FileUploadV1.vue';
import TimelineCard from '../../components/formFlow/TimelineCard.vue';
import { localUserInfo } from '../../helper/localUserInfo';

import initForm from '../../helper/initForm';
import isEditable from '../../helper/isEditable';

import ReviewChips from '../../components/formFlow/formComponent/ReviewChips.vue';
import ActionButtons from '../../components/formFlow/ActionButtons.vue';

export default {
    name: 'flow_3004',
    directives: { print },
    components: {
        ActionButtons,
        ReviewChips,
        FileUploadV1,
        TimelineCard,
    },
    data() {
        return {
            isShowFunctionArea: false,
            isEditable: false,
            isShowTimeLine: false,

            datePickerMenu: false,

            jobId: '',
            nodeId: '',
            workflowId: '',

            formInfo: {},
            jobInfo: {},
            nodeInfo: {},

            currentComment: '',
            currentCommentField: '',

            leaveReason: [
                { text: '事假', value: 1 },
                { text: '病假', value: 2 },
                { text: '婚假', value: 3 },
                { text: '丧假', value: 4 },
                { text: '年休', value: 5 },
                { text: '工伤', value: 6 },
                { text: '产假', value: 7 },
                { text: '护理假', value: 8 },
                { text: '其他', value: 9 },
            ],

            formData: {
                title: '',

                name: '',
                department: '',
                workTime: '',
                position: '',
                vacationType: 1,
                vacationReason: '',
                vacationDate: new Date(Date.now())
                    .toLocaleDateString('zh-CN')
                    .replace(/\//g, '-'),
                vacationDays: '',
                workHandoverArrangement: '',
                takingOverPersonSignature: '',

                approveSign: [],
                departmentSign: [],
                originSign: [],
                promoterSign: '',

                date: new Date(Date.now())
                    .toLocaleDateString('zh-CN')
                    .replace(/\//g, '-'),

                mainFiles: '',
                attachFiles: '',
            },

            userId: localUserInfo.id,
            userName: localUserInfo.fullName,

            rules: {
                required: (value) => !!value || '此条目不能为空',
            },
        };
    },
    methods: {
        getMainFileInfo(filesList) {
            this.formData.mainFiles = filesList;
        },

        getAttachFileInfo(filesList) {
            this.formData.attachFiles = filesList;
        },

        getChipValue(chip) {
            this.currentComment = chip;
        },

        async initHtmlForm() {
            if (this.$route.query.workflowId) {
                this.jobId = this.$route.query.jobId;
                this.nodeId = this.$route.query.nodeId;
                this.workflowId = this.$route.query.workflowId;

                const initInfo = await initForm(
                    this.$axios,
                    this.jobId,
                    this.nodeId,
                    this.workflowId
                );

                this.formInfo = initInfo.formInfo;
                this.jobInfo = initInfo.jobInfo;
                this.nodeInfo = initInfo.nodeInfo;
                this.formData = initInfo.formInfo.formData;
                this.reviewUserList = this.formData.reviewUserList;

                this.isEditable = isEditable(this.jobInfo.jobList, this.userId);
                this.isShowFunctionArea = true;

                if (!this.isEditable) return;
                if (this.jobInfo.isEnding) return;
                if (this.jobInfo.isCreating) return;

                this.currentCommentField =
                    this.nodeInfo.nodeTitle === '部门经理'
                        ? 'departmentSign'
                        : 'originSign';

                if (!this.jobInfo.isStaging) {
                    this.formData[this.currentCommentField].unshift({
                        name: this.userName,
                        content: '',
                    });
                }

                return;
            }

            this.formData.title = `${this.userName}的请假申请`;
            this.formData.promoterSign = this.userName;
            this.formInfo.formData = this.formData;
            this.jobInfo.isCreating = true;
            this.isEditable = true;
            this.isShowFunctionArea = true;
        },
    },
    mounted() {
        this.initHtmlForm();
    },

    watch: {
        currentComment(newValue) {
            this.formData[this.currentCommentField][0].content = newValue;
        },
    },
};
</script>

<style scoped>
span {
    word-wrap: anywhere;
}

.borderB {
    border-bottom: 1px solid black;
}

.borderR {
    border-right: 1px solid black;
    flex: 1;
}

.flexWrap {
    width: 100%;
    height: 1px;
    background-color: transparent;
    border-bottom: 1px solid black;
}
</style>
