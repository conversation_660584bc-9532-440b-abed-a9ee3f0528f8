<template>
    <v-container fluid>
        <v-row>
            <v-col
                cols="12"
                lg="8"
                style="margin: 0 auto; max-width: 960px"
                xs="12"
            >
                <v-card class="pa-4">
                    <v-card-title>
                        <v-btn
                            color="primary"
                            icon
                            small
                            @click="$router.go(-1)"
                        >
                            <v-icon>mdi-arrow-left</v-icon>
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                            v-print="'#printForm'"
                            color="primary"
                            icon
                            small
                        >
                            <v-icon>mdi-printer</v-icon>
                        </v-btn>
                    </v-card-title>
                    <v-card-text>
                        <form
                            id="printForm"
                            style="
                                color: black;
                                line-height: 32px;
                                display: grid;
                                grid-template-columns: repeat(4, 1fr);
                                gap: 1px;
                                background: black;
                                border: black solid 1px;
                                text-align: center;
                            "
                        >
                            <div style="grid-column: span 4">
                                <h1 style="text-align: center">文件传阅单</h1>
                                <div style="text-align: right">
                                    <span v-if="formInfo.docNumber"
                                        >编号:&nbsp;{{
                                            formInfo.docNumber
                                        }}</span
                                    >
                                </div>
                            </div>

                            <div>标题</div>

                            <div style="grid-column: span 3">
                                {{ formData.title }}
                            </div>

                            <div>文件号</div>

                            <div style="grid-column: span 3">
                                {{ formData.number }}
                            </div>

                            <div
                                v-for="user in reviewUserList"
                                :key="user.uid"
                                class="grid1_3"
                            >
                                <div>{{ user.name }}批示:</div>
                                <div style="grid-column: span 3">
                                    <div
                                        v-for="comment in formData[user.pinyin]"
                                        :key="comment.index"
                                    >
                                        <span>{{ comment.content }}</span>
                                    </div>
                                </div>
                            </div>

                            <div class="grid1_3">
                                <div>主办部门意见:</div>
                                <div style="grid-column: span 3">
                                    <div
                                        v-for="item in formData.mainOrganizers"
                                        :key="item.index"
                                    >
                                        <span
                                            >{{ item.name }}
                                            {{ item.content }}</span
                                        >
                                    </div>
                                </div>
                            </div>

                            <div style="grid-column: span 4; text-align: right">
                                日期:&nbsp;{{ formData.date }}
                            </div>
                        </form>
                    </v-card-text>
                </v-card>
            </v-col>
            <v-col cols="12" lg="4" xs="12">
                <v-card class="pa-4">
                    <v-tabs>
                        <v-tab @click="isShowTimeLine = false">内容</v-tab>
                        <v-tab @click="isShowTimeLine = true">时间线</v-tab>
                    </v-tabs>
                    <v-card-text v-show="!isShowTimeLine">
                        <v-form v-if="jobInfo.isCreating">
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.title"
                                        :rules="[rules.required]"
                                        dense
                                        label="标题"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.number"
                                        :rules="[rules.required]"
                                        dense
                                        label="文号"
                                    />
                                </v-col>
                            </v-row>
                        </v-form>
                        <v-form
                            v-if="
                                isEditable &&
                                (jobInfo.isReviewing || jobInfo.isSigning) &&
                                !jobInfo.isEnding
                            "
                        >
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="currentComment"
                                        :rules="[rules.required]"
                                        dense
                                        label="请输入批示"
                                    />
                                </v-col>
                            </v-row>
                            <ReviewChips :getChipValue="getChipValue" />
                        </v-form>
                        <v-row>
                            <v-col>
                                正文:
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.mainFiles"
                                    :getFileInfo="getMainFileInfo"
                                />
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                附件:
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.attachFiles"
                                    :getFileInfo="getAttachFileInfo"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>
                    <ActionButtons
                        v-if="isShowFunctionArea"
                        :currentComment="currentComment"
                        :currentCommentField="currentCommentField"
                        :formInfo="formInfo"
                        :isEditable="isEditable"
                        :isShowTimeLine="isShowTimeLine"
                        :jobInfo="jobInfo"
                        :nodeInfo="nodeInfo"
                        :workflowId="workflowId"
                    />
                    <v-card-text v-if="isShowTimeLine">
                        <TimelineCard
                            :jobList="jobInfo.jobList"
                            :nodeList="nodeInfo.nodeList"
                        />
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import print from 'vue-print-nb';
import FileUploadV1 from '../../components/FileUploadV1.vue';
import TimelineCard from '../../components/formFlow/TimelineCard.vue';
import { localUserInfo } from '../../helper/localUserInfo';

import initForm from '../../helper/initForm';
import isEditable from '../../helper/isEditable';

import ReviewChips from '../../components/formFlow/formComponent/ReviewChips.vue';
import ActionButtons from '../../components/formFlow/ActionButtons.vue';

export default {
    name: 'flow_3002',
    directives: { print },
    components: {
        ActionButtons,
        TimelineCard,
        ReviewChips,
        FileUploadV1,
    },
    data() {
        return {
            isShowFunctionArea: false,
            isEditable: false,
            isShowTimeLine: false,

            jobId: '',
            nodeId: '',
            workflowId: '',

            formInfo: {},
            jobInfo: {},
            nodeInfo: {},

            currentComment: '',
            currentCommentField: '',

            formData: {
                title: '',
                number: '',
                date: new Date(Date.now())
                    .toLocaleDateString('zh-CN')
                    .replace(/\//g, '-'),

                DZQ: [],
                GS: [],
                WHH: [],
                GYH: [],
                HJ: [],
                LL: [],

                mainOrganizers: [],

                mainFiles: '',
                attachFiles: '',

                reviewUserList: [],
            },

            reviewUserList: [
                {
                    uid: 'd604ff92-48be-11eb-8f84-7b9620caa627',
                    name: '段赵清',
                    pinyin: 'DZQ',
                },
                // {
                //     uid: 'd6d9b4b0-48be-11eb-8f84-7b9620caa627',
                //     name: '郭盛',
                //     pinyin: 'GS',
                // },
                {
                    uid: 'fdf4e9c0-64c3-11ed-84e3-5bbd2b44807b',
                    name: '吴海晖',
                    pinyin: 'WHH',
                },
                {
                    uid: 'd6d96690-48be-11eb-8f84-7b9620caa627',
                    name: '耿跃华',
                    pinyin: 'GYH',
                },
                {
                    uid: 'd5999392-48be-11eb-8f84-7b9620caa627',
                    name: '何军',
                    pinyin: 'HJ',
                },
                {
                    uid: 'fdf7cff0-64c3-11ed-84e3-5bbd2b44807b',
                    name: '刘磊',
                    pinyin: 'LL',
                },
            ],

            userId: localUserInfo.id,
            userName: localUserInfo.fullName,

            rules: {
                required: (value) => !!value || '此条目不能为空',
            },
        };
    },
    methods: {
        getMainFileInfo(filesList) {
            this.formData.mainFiles = filesList;
        },

        getAttachFileInfo(filesList) {
            this.formData.attachFiles = filesList;
        },

        getChipValue(chip) {
            this.currentComment = chip;
        },

        async initHtmlForm() {
            if (this.$route.query.workflowId) {
                this.workflowId = this.$route.query.workflowId;
                this.nodeId = this.$route.query.nodeId;
                this.jobId = this.$route.query.jobId;

                const initInfo = await initForm(
                    this.$axios,
                    this.jobId,
                    this.nodeId,
                    this.workflowId
                );

                this.formInfo = initInfo.formInfo;
                this.jobInfo = initInfo.jobInfo;
                this.nodeInfo = initInfo.nodeInfo;
                this.formData = initInfo.formInfo.formData;
                this.reviewUserList = this.formData.reviewUserList;

                this.isEditable = isEditable(this.jobInfo.jobList, this.userId);
                this.isShowFunctionArea = true;

                if (!this.isEditable) return;
                if (this.jobInfo.isEnding) return;
                if (this.jobInfo.isCreating) return;

                const userOnForm = this.formData.reviewUserList.find(
                    (item) => item.uid === this.userId
                );

                this.currentCommentField = userOnForm
                    ? userOnForm.pinyin
                    : 'mainOrganizers';

                if (!this.jobInfo.isStaging) {
                    this.formData[this.currentCommentField].unshift({
                        name: this.userName,
                        content: '',
                    });
                }

                return;
            }

            this.formData.reviewUserList = this.reviewUserList;
            this.formInfo.formData = this.formData;
            this.jobInfo.isCreating = true;
            this.isEditable = true;
            this.isShowFunctionArea = true;
        },
    },
    mounted() {
        this.initHtmlForm();
    },
    watch: {
        currentComment(newValue) {
            this.formData[this.currentCommentField][0].content = newValue;
        },
    },
};
</script>

<style scoped>
div {
    background-color: white;
}

.grid1_3 {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-column: span 4;
    gap: 1px;
    background-color: black;
    min-height: 64px;
}
</style>
