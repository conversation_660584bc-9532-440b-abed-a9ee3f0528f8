<template>
    <v-container fluid>
        <v-row>
            <v-col
                cols="12"
                lg="8"
                style="margin: 0 auto; max-width: 960px"
                xs="12"
            >
                <v-card class="pa-4">
                    <v-card-title>
                        <v-btn
                            color="primary"
                            icon
                            small
                            @click="$router.go(-1)"
                        >
                            <v-icon>mdi-arrow-left</v-icon>
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                            v-print="'#printForm'"
                            color="primary"
                            icon
                            small
                        >
                            <v-icon>mdi-printer</v-icon>
                        </v-btn>
                    </v-card-title>
                    <v-card-text>
                        <form
                            id="printForm"
                            style="
                                color: black;
                                line-height: 32px;
                                display: grid;
                                grid-template-columns: 1fr;
                                gap: 1px;
                                background: black;
                            "
                        >
                            <div style="text-align: center">
                                <h1>会议纪要传签单</h1>
                            </div>
                            <div>
                                部门: {{ formData.formDepartment
                                }}<span
                                    v-if="formInfo.docNumber"
                                    style="float: right"
                                    >编号: {{ formInfo.docNumber }}</span
                                >
                            </div>
                            <div>标题: {{ formData.title }}</div>
                            <div
                                v-for="user in formData.reviewUserList"
                                :key="user.uid"
                                style="min-height: 96px"
                            >
                                <div>{{ user.name }}批示:</div>
                                <div
                                    v-for="comment in formData[user.pinyin]"
                                    :key="comment.index"
                                >
                                    <div>{{ comment.content }}</div>
                                </div>
                            </div>
                            <div style="min-height: 96px">
                                部门经理签字:
                                <div
                                    v-for="comment in formData.mainOrganizers"
                                    :key="comment.index"
                                >
                                    <div>
                                        {{ comment.name }}:
                                        {{ comment.content }}
                                    </div>
                                </div>
                            </div>
                            <div>
                                承办人: {{ formData.promoter
                                }}<span style="float: right"
                                    >日期: {{ formData.date }}</span
                                >
                            </div>
                        </form>
                    </v-card-text>
                </v-card>
            </v-col>

            <v-col cols="12" lg="4" xs="12">
                <v-card class="pa-4">
                    <v-card-title>
                        <v-tabs>
                            <v-tab @click="isShowTimeLine = false">内容</v-tab>
                            <v-tab @click="isShowTimeLine = true">时间线</v-tab>
                        </v-tabs>
                    </v-card-title>
                    <v-card-text v-show="!isShowTimeLine">
                        <v-form v-if="jobInfo.isCreating">
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.title"
                                        :rules="[inputRules.required]"
                                        dense
                                        label="标题"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                        </v-form>
                        <v-form
                            v-if="
                                isEditable &&
                                (jobInfo.isReviewing || jobInfo.isSigning) &&
                                !jobInfo.isEnding
                            "
                        >
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="currentComment"
                                        :rules="[inputRules.required]"
                                        dense
                                        label="请输入批示"
                                    />
                                </v-col>
                            </v-row>
                            <ReviewChips :getChipValue="getChipValue" />
                        </v-form>
                        <v-row>
                            <v-col>
                                正文:
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.mainFiles"
                                    :getFileInfo="getMainFileInfo"
                                />
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                附件:
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.attachFiles"
                                    :getFileInfo="getAttachFileInfo"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>

                    <ActionButtons
                        v-if="isShowFunctionArea"
                        :currentComment="currentComment"
                        :currentCommentField="currentCommentField"
                        :formInfo="formInfo"
                        :isEditable="isEditable"
                        :isShowTimeLine="isShowTimeLine"
                        :jobInfo="jobInfo"
                        :nodeInfo="nodeInfo"
                        :workflowId="workflowId"
                    />
                    <v-card-text v-if="isShowTimeLine">
                        <TimelineCard
                            :jobList="jobInfo.jobList"
                            :nodeList="nodeInfo.nodeList"
                        />
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import print from 'vue-print-nb';
import FileUploadV1 from '../../components/FileUploadV1.vue';
import TimelineCard from '../../components/formFlow/TimelineCard.vue';
import { localUserInfo } from '../../helper/localUserInfo';

import initForm from '../../helper/initForm';
import isEditable from '../../helper/isEditable';

import ReviewChips from '../../components/formFlow/formComponent/ReviewChips.vue';
import ActionButtons from '../../components/formFlow/ActionButtons.vue';

export default {
    name: 'flow_3010',
    components: {
        ReviewChips,
        FileUploadV1,
        ActionButtons,
        TimelineCard,
    },
    directives: { print },
    data() {
        return {
            isShowFunctionArea: false,
            isEditable: false,
            isShowTimeLine: false,

            jobId: '',
            nodeId: '',
            workflowId: '',

            formInfo: {},
            jobInfo: {},
            nodeInfo: {},

            currentComment: '',
            currentCommentField: '',

            formData: {
                formDepartment: '',
                formNumber: '',

                title: '',
                promoter: '',
                date: new Date(Date.now())
                    .toLocaleDateString('zh-CN')
                    .replace(/\//g, '-'),

                DZQ: [],
                GS: [],
                WHH: [],
                GYH: [],
                WL: [],
                HJ: [],
                LL: [],

                mainOrganizers: [],

                mainFiles: '',
                attachFiles: '',

                reviewUserList: [],
            },

            reviewUserList: [
                {
                    uid: 'd604ff92-48be-11eb-8f84-7b9620caa627',
                    name: '段赵清',
                    pinyin: 'DZQ',
                },
                // {
                //     uid: 'd6d9b4b0-48be-11eb-8f84-7b9620caa627',
                //     name: '郭\xa0\xa0\xa0\xa0盛',
                //     pinyin: 'GS',
                // },
                {
                    uid: 'fdf4e9c0-64c3-11ed-84e3-5bbd2b44807b',
                    name: '吴海晖',
                    pinyin: 'WHH',
                },
                {
                    uid: 'd6d96690-48be-11eb-8f84-7b9620caa627',
                    name: '耿跃华',
                    pinyin: 'GYH',
                },
                // {
                //     uid: 'd6d9dbc2-48be-11eb-8f84-7b9620caa627',
                //     name: '王\xa0\xa0\xa0\xa0雷',
                //     pinyin: 'WL',
                // },
                {
                    uid: 'd5999392-48be-11eb-8f84-7b9620caa627',
                    name: '何\xa0\xa0\xa0\xa0军',
                    pinyin: 'HJ',
                },
                {
                    uid: 'fdf7cff0-64c3-11ed-84e3-5bbd2b44807b',
                    name: '刘\xa0\xa0\xa0\xa0磊',
                    pinyin: 'LL',
                },
            ],

            userId: localUserInfo.id,
            userName: localUserInfo.fullName,

            inputRules: {
                required: (value) => !!value || '此条目不能为空',
            },
        };
    },

    methods: {
        getMainFileInfo(filesList) {
            this.formData.mainFiles = filesList;
        },
        getAttachFileInfo(filesList) {
            this.formData.attachFiles = filesList;
        },

        getChipValue(chip) {
            this.currentComment = chip;
        },

        async initHtmlForm() {
            if (this.$route.query.workflowId) {
                this.jobId = this.$route.query.jobId;
                this.nodeId = this.$route.query.nodeId;
                this.workflowId = this.$route.query.workflowId;

                const initInfo = await initForm(
                    this.$axios,
                    this.jobId,
                    this.nodeId,
                    this.workflowId
                );

                this.jobInfo = initInfo.jobInfo;
                this.formInfo = initInfo.formInfo;
                this.nodeInfo = initInfo.nodeInfo;
                this.formData = initInfo.formInfo.formData;

                this.isEditable = isEditable(this.jobInfo.jobList, this.userId);
                this.isShowFunctionArea = true;

                if (!this.isEditable) return;
                if (this.jobInfo.isEnding) return;
                if (this.jobInfo.isCreating) return;

                const userOnForm = this.formData.reviewUserList.find(
                    (item) => item.uid === this.userId
                );

                this.currentCommentField = userOnForm
                    ? userOnForm.pinyin
                    : 'mainOrganizers';

                if (!this.jobInfo.isStaging) {
                    this.formData[this.currentCommentField].unshift({
                        name: this.userName,
                        content: '',
                    });
                }

                return;
            }

            this.formData.formDepartment = localUserInfo.department.name;
            this.formData.promoter = this.userName;
            this.formData.tel = localUserInfo.mobile;
            this.formData.reviewUserList = this.reviewUserList;

            this.formInfo.formData = this.formData;
            this.jobInfo.isCreating = true;

            this.isEditable = true;
            this.isShowFunctionArea = true;
        },
    },

    mounted() {
        this.initHtmlForm();
    },

    watch: {
        currentComment(newValue) {
            this.formData[this.currentCommentField][0].content = newValue;
        },
    },
};
</script>

<style scoped>
div {
    background-color: white;
}
</style>
