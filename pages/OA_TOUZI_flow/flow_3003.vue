<template>
    <v-container fluid>
        <v-row>
            <v-col
                cols="12"
                lg="8"
                style="margin: 0 auto; max-width: 960px"
                xs="12"
            >
                <v-card class="pa-4">
                    <v-card-title>
                        <v-btn
                            color="primary"
                            icon
                            small
                            @click="$router.go(-1)"
                        >
                            <v-icon>mdi-arrow-left</v-icon>
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                            v-print="'#printForm'"
                            color="primary"
                            icon
                            small
                        >
                            <v-icon>mdi-printer</v-icon>
                        </v-btn>
                    </v-card-title>
                    <v-card-text>
                        <form
                            id="printForm"
                            style="
                                color: black;
                                line-height: 32px;
                                display: grid;
                                grid-template-columns: repeat(4, 1fr);
                                gap: 1px;
                                background: black;
                                border: black solid 1px;
                                text-align: center;
                            "
                        >
                            <div style="grid-column: span 4">
                                <h1>用印审批单</h1>
                            </div>

                            <div>申请用印单位</div>
                            <div>{{ formData.sealRequested }}</div>
                            <div>申请人签名(手签写)</div>
                            <div></div>

                            <div class="grid1_3">
                                <div>用何印章</div>
                                <div style="grid-column: span 3">
                                    {{ formData.sealName }}
                                </div>
                            </div>

                            <div class="grid1_3">
                                <div>用印数量</div>
                                <div style="grid-column: span 3">
                                    {{ formData.sealAmount }}
                                </div>
                            </div>

                            <div class="grid1_3">
                                <div>用印事由</div>
                                <div style="grid-column: span 3">
                                    {{ formData.sealReason }}
                                </div>
                            </div>

                            <div class="grid1_3">
                                <div>主办部门领导审批</div>
                                <div style="grid-column: span 3">
                                    <div
                                        v-for="item in formData.mainOrganizers"
                                        :key="item.index"
                                    >
                                        <span>
                                            {{ item.name }}:
                                            {{ item.content }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div>监督用印单位</div>
                            <div>综合人事部</div>
                            <div>监章人姓名(手写)</div>
                            <div></div>

                            <div class="grid1_3">
                                <div>公司领导审批</div>
                                <div style="grid-column: span 3">
                                    <div
                                        v-for="item in formData.companyHeader"
                                        :key="item.index"
                                    >
                                        <span
                                            >{{ item.name }}:
                                            {{ item.content }}</span
                                        >
                                    </div>
                                </div>
                            </div>

                            <div class="grid1_3" style="min-height: 32px">
                                <div>日期</div>
                                <div style="grid-column: span 3">
                                    {{ formData.date }}
                                </div>
                            </div>
                        </form>
                    </v-card-text>
                </v-card>
            </v-col>
            <v-col cols="12" lg="4" xs="12">
                <v-card class="pa-4">
                    <v-tabs>
                        <v-tab @click="isShowTimeLine = false">内容</v-tab>
                        <v-tab @click="isShowTimeLine = true">时间线</v-tab>
                    </v-tabs>
                    <v-card-text v-show="!isShowTimeLine">
                        <v-form v-if="jobInfo.isCreating">
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.sealRequested"
                                        :rules="[rules.required]"
                                        dense
                                        label="申请用印单位"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.sealName"
                                        :rules="[rules.required]"
                                        dense
                                        label="用何印章"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.sealAmount"
                                        :rules="[rules.required]"
                                        dense
                                        label="用印数量"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.sealReason"
                                        :rules="[rules.required]"
                                        dense
                                        label="用印事由"
                                    />
                                </v-col>
                            </v-row>
                        </v-form>
                        <v-form
                            v-if="
                                isEditable &&
                                (jobInfo.isReviewing || jobInfo.isSigning) &&
                                !jobInfo.isEnding
                            "
                        >
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="currentComment"
                                        :rules="[rules.required]"
                                        dense
                                        label="请输入批示"
                                    />
                                </v-col>
                            </v-row>
                            <ReviewChips :getChipValue="getChipValue" />
                        </v-form>
                        <v-row>
                            <v-col>
                                正文:
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.mainFiles"
                                    :getFileInfo="getMainFileInfo"
                                />
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                附件:
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.attachFiles"
                                    :getFileInfo="getAttachFileInfo"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>

                    <ActionButtons
                        v-if="isShowFunctionArea"
                        :currentComment="currentComment"
                        :currentCommentField="currentCommentField"
                        :formInfo="formInfo"
                        :isEditable="isEditable"
                        :isShowTimeLine="isShowTimeLine"
                        :jobInfo="jobInfo"
                        :nodeInfo="nodeInfo"
                        :workflowId="workflowId"
                    />
                    <v-card-text v-if="isShowTimeLine">
                        <TimelineCard
                            :jobList="jobInfo.jobList"
                            :nodeList="nodeInfo.nodeList"
                        />
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import print from 'vue-print-nb';
import FileUploadV1 from '../../components/FileUploadV1.vue';
import TimelineCard from '../../components/formFlow/TimelineCard.vue';
import { localUserInfo } from '../../helper/localUserInfo';

import initForm from '../../helper/initForm';
import isEditable from '../../helper/isEditable';

import ReviewChips from '../../components/formFlow/formComponent/ReviewChips.vue';
import ActionButtons from '../../components/formFlow/ActionButtons.vue';

export default {
    name: 'flow_3003',
    directives: { print },
    components: {
        ReviewChips,
        FileUploadV1,
        ActionButtons,
        TimelineCard,
    },
    data() {
        return {
            isShowFunctionArea: false,
            isEditable: false,
            isShowTimeLine: false,

            jobId: '',
            nodeId: '',
            workflowId: '',

            formInfo: {},
            jobInfo: {},
            nodeInfo: {},

            currentComment: '',
            currentCommentField: '',

            formData: {
                title: '',

                sealRequested: '',
                sealName: '',
                sealAmount: '',
                sealReason: '',

                mainOrganizers: [],
                companyHeader: [],

                date: new Date(Date.now())
                    .toLocaleDateString('zh-CN')
                    .replace(/\//g, '-'),

                mainFiles: '',
                attachFiles: '',
            },

            userId: localUserInfo.id,
            userName: localUserInfo.fullName,

            rules: {
                required: (value) => !!value || '此条目不能为空',
            },
        };
    },
    methods: {
        getMainFileInfo(filesList) {
            this.formData.mainFiles = filesList;
        },

        getAttachFileInfo(filesList) {
            this.formData.attachFiles = filesList;
        },

        getChipValue(chip) {
            this.currentComment = chip;
        },

        async initHtmlForm() {
            if (this.$route.query.workflowId) {
                this.jobId = this.$route.query.jobId;
                this.nodeId = this.$route.query.nodeId;
                this.workflowId = this.$route.query.workflowId;

                const initInfo = await initForm(
                    this.$axios,
                    this.jobId,
                    this.nodeId,
                    this.workflowId
                );

                this.formInfo = initInfo.formInfo;
                this.jobInfo = initInfo.jobInfo;
                this.nodeInfo = initInfo.nodeInfo;
                this.formData = initInfo.formInfo.formData;
                this.reviewUserList = this.formData.reviewUserList;

                this.isEditable = isEditable(this.jobInfo.jobList, this.userId);
                this.isShowFunctionArea = true;

                if (!this.isEditable) return;
                if (this.jobInfo.isEnding) return;
                if (this.jobInfo.isCreating) return;

                this.currentCommentField =
                    this.nodeInfo.nodeTitle === '总经理'
                        ? 'companyHeader'
                        : 'mainOrganizers';

                if (!this.jobInfo.isStaging) {
                    this.formData[this.currentCommentField].unshift({
                        name: this.userName,
                        content: '',
                    });
                }

                return;
            }

            this.formInfo.formData = this.formData;
            this.jobInfo.isCreating = true;
            this.isEditable = true;
            this.isShowFunctionArea = true;
        },
    },
    mounted() {
        this.initHtmlForm();
    },
    watch: {
        'formData.sealReason': function (newValue) {
            this.formData.title = newValue;
        },

        currentComment(newValue) {
            this.formData[this.currentCommentField][0].content = newValue;
        },
    },
};
</script>

<style scoped>
div {
    background-color: white;
}

.grid1_3 {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-column: span 4;
    gap: 1px;
    background-color: black;
    min-height: 64px;
}
</style>
