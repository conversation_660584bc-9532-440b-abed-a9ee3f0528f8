<template>
    <v-container fluid>
        <v-row>
            <v-col>
                <ExpansionPanels />
            </v-col>
            <!--            <v-col cols="9">-->
            <!--                <TableList />-->
            <!--            </v-col>-->
        </v-row>
    </v-container>
</template>

<script>
import ExpansionPanels from '../components/processCenter/ExpansionPanels.vue';
// import TableList from '../components/processCenter/TableList.vue';

export default {
    name: 'processCenter',
    components: {
        ExpansionPanels,
        // TableList,
    },
};
</script>

<style scoped></style>
