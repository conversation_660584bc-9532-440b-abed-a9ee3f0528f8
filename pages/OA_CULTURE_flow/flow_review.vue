<template>
    <v-container fluid>
        <v-row>
            <v-col
                cols="12"
                lg="8"
                style="margin: 0 auto; max-width: 960px"
                xs="12"
            >
                <v-card class="pa-4">
                    <v-card-title>
                        <v-btn
                            color="primary"
                            icon
                            small
                            @click="$router.go(-1)"
                        >
                            <v-icon>mdi-arrow-left</v-icon>
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                            v-print="'#printForm'"
                            color="primary"
                            icon
                            small
                        >
                            <v-icon>mdi-printer</v-icon>
                        </v-btn>
                    </v-card-title>
                    <v-card-text>
                        <form
                            id="printForm"
                            style="line-height: 32px; color: red"
                        >
                            <h1 style="text-align: center; margin-bottom: 32px">
                                呈&nbsp;&nbsp;&nbsp;&nbsp;批&nbsp;&nbsp;&nbsp;&nbsp;件
                            </h1>
                            <div class="borderB">
                                保利文化股份集团有限公司
                                <div
                                    v-if="docNumber && docType"
                                    style="float: right; display: inline-block"
                                >
                                    呈&nbsp;<span>{{ docType }}</span
                                    >&nbsp;字第&nbsp;<span>{{ docNumber }}</span
                                    >&nbsp;号
                                </div>
                            </div>
                            <div class="borderB">
                                题目:<span>{{ formData.title }}</span>
                            </div>
                            <div class="borderB" style="min-height: 96px">
                                <div>郭文鹏:</div>
                                <div
                                    v-for="item in formData.GWP"
                                    :key="item.index"
                                >
                                    <span>{{ item.content }}</span>
                                </div>
                            </div>
                            <div
                                class="borderB"
                                style="min-height: 96px; display: flex"
                            >
                                <div class="borderR">
                                    <div>李卫强:</div>
                                    <div
                                        v-for="item in formData.LWQ"
                                        :key="item.index"
                                    >
                                        <span>{{ item.content }}</span>
                                    </div>
                                </div>
                                <div style="flex: 1">
                                    <div>徐碚:</div>
                                    <div
                                        v-for="item in formData.XB"
                                        :key="item.index"
                                    >
                                        <span>{{ item.content }}</span>
                                    </div>
                                </div>
                            </div>
                            <div
                                class="borderB"
                                style="min-height: 96px; display: flex"
                            >
                                <div class="borderR">
                                    <div>王蔚:</div>
                                    <div
                                        v-for="item in formData.WW"
                                        :key="item.index"
                                    >
                                        <span>{{ item.content }}</span>
                                    </div>
                                </div>
                                <div style="flex: 1">
                                    <div>李文亮:</div>
                                    <div
                                        v-for="item in formData.LWL"
                                        :key="item.index"
                                    >
                                        <span>{{ item.content }}</span>
                                    </div>
                                </div>
                            </div>
                            <div
                                class="borderB"
                                style="min-height: 96px; display: flex"
                            >
                                <div class="borderR">
                                    <div>赵琳:</div>
                                    <div
                                        v-for="item in formData.ZL"
                                        :key="item.index"
                                    >
                                        <span>{{ item.content }}</span>
                                    </div>
                                </div>
                                <div style="flex: 1"></div>
                            </div>
                            <div class="borderB" style="min-height: 96px">
                                <div>主办部门意见:</div>
                                <div
                                    v-for="item in formData.mainOrganizers"
                                    :key="item.index"
                                >
                                    <span
                                        >{{ item.name }}:{{
                                            item.content
                                        }}</span
                                    >
                                </div>
                            </div>
                            <div style="display: flex">
                                <div style="flex: 1">
                                    承办人:<span>{{ formData.promoter }}</span>
                                </div>
                                <div style="flex: 1">
                                    电话:<span>{{ formData.tel }}</span>
                                </div>
                            </div>
                        </form>
                    </v-card-text>
                </v-card>
            </v-col>

            <v-col cols="12" lg="4" xs="12">
                <v-card class="pa-4">
                    <v-tabs>
                        <v-tab @click="tab = 0">内容</v-tab>
                        <v-tab @click="tab = 1">时间线</v-tab>
                    </v-tabs>
                    <v-card-text v-show="tab === 0">
                        <v-form
                            v-if="
                                !$route.query.workflowId || jobList.length <= 1
                            "
                        >
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.title"
                                        :rules="[rules.required]"
                                        dense
                                        label="题目"
                                    />
                                </v-col>
                            </v-row>
                        </v-form>
                        <v-form
                            v-if="
                                $route.query.workflowId &&
                                editAble &&
                                jobList.length > 1
                            "
                        >
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="reviewComment"
                                        :rules="[rules.required]"
                                        dense
                                        label="请输入批示"
                                    />
                                </v-col>
                            </v-row>
                            <ReviewChips :getChipValue="getChipValue" />
                        </v-form>
                        <v-row>
                            <v-col>
                                正文：
                                <FileUploadV1
                                    :allowUpload="
                                        jobList.length <= 1
                                            ? true
                                            : nodePermission.fileModify
                                    "
                                    :filesProps="formData.mainFiles"
                                    :getFileInfo="getMainFileInfo"
                                />
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                附件：
                                <FileUploadV1
                                    :allowUpload="
                                        jobList.length <= 1
                                            ? true
                                            : nodePermission.fileModify
                                    "
                                    :filesProps="formData.attachFiles"
                                    :getFileInfo="getAttachFileInfo"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>

                    <v-card-actions
                        v-if="tab === 0 && editAble && jobList.length <= 1"
                    >
                        <FlowCreateButton
                            :formData="formData"
                            :formIdProp="formId"
                            :jobIdProp="jobId"
                            :nodeIdProp="nodeId"
                            :workflowIdProp="workflowId"
                        />
                    </v-card-actions>

                    <v-card-actions
                        v-if="tab === 0 && editAble && jobList.length > 1"
                    >
                        <StagingButton
                            :currentComment="reviewComment"
                            :formData="formData"
                            :formId="formId"
                            :jobId="jobId"
                        />
                        <SubmitButton
                            v-if="actionType !== '2'"
                            :currentComment="reviewComment"
                            :formData="formData"
                            :formId="formId"
                            :jobId="jobId"
                            :nodeId="nodeId"
                            :workflowId="workflowId"
                        />
                        <CountersignButton
                            v-if="
                                actionType !== '2' &&
                                nodePermission.countersignCreate
                            "
                            :currentComment="reviewComment"
                            :formData="formData"
                            :formId="formId"
                            :jobId="jobId"
                            :nodeId="nodeId"
                            :workflowId="workflowId"
                        />
                        <ReadButton
                            v-if="nodePermission.readCreate"
                            :currentComment="reviewComment"
                            :nodeId="nodeId"
                            :workflowId="workflowId"
                        />
                        <TransferButton
                            v-if="nodePermission.transferCreate"
                            :formId="formId"
                            :nodeId="nodeId"
                            :workflowId="workflowId"
                        />
                        <CountersigningButton
                            v-if="actionType === '2'"
                            :currentComment="reviewComment"
                            :formData="formData"
                            :formId="formId"
                            :jobId="jobId"
                        />
                        <BackingButton
                            v-if="nodePermission.returnCreate"
                            :formId="formId"
                            :nodeId="nodeId"
                            :workflowId="workflowId"
                        />
                    </v-card-actions>

                    <v-card-text v-if="tab === 1">
                        <TimelineCard
                            :jobList="jobList"
                            :workflowId="workflowId"
                        />
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import print from 'vue-print-nb';
import AppConfig from '../../constants/AppConfig';
import initForm from '../../helper/initForm';
import isEditable from '../../helper/isEditable';

import ReviewChips from '../../components/formFlow/formComponent/ReviewChips.vue';
import FileUploadV1 from '../../components/FileUploadV1.vue';

import FlowCreateButton from '../../components/formFlow/formFunction/FormCreate.vue';
import StagingButton from '../../components/formFlow/formFunction/StagingCreate.vue';
import SubmitButton from '../../components/formFlow/formFunction/FormSubmit.vue';
import CountersignButton from '../../components/formFlow/formFunction/CountersignCreate.vue';
import ReadButton from '../../components/formFlow/formFunction/ReadCreate.vue';
import TransferButton from '../../components/formFlow/formFunction/TransferCreate.vue';
import BackingButton from '../../components/formFlow/formFunction/BackingCreate.vue';
import CountersigningButton from '../../components/formFlow/formFunction/CountersignEnd.vue';

import TimelineCard from '../../components/formFlow/TimelineCard.vue';

export default {
    name: 'flow_review',
    directives: { print },
    components: {
        ReviewChips,
        FileUploadV1,
        FlowCreateButton,
        StagingButton,
        SubmitButton,
        CountersignButton,
        ReadButton,
        TransferButton,
        BackingButton,
        CountersigningButton,
        TimelineCard,
    },
    data() {
        return {
            editAble: false,
            nodePermission: {},
            docNumber: '',
            docType: '',

            currentUser: {},

            formData: {
                title: '',

                promoter: '',
                tel: '',

                GWP: [],
                LWQ: [],
                XB: [],
                WW: [],
                LWL: [],
                ZL: [],

                mainOrganizers: [],

                mainFiles: '',
                attachFiles: '',
            },

            tab: 0,

            rules: {
                required: (value) => !!value || '此条目不能为空',
            },

            reviewComment: '',

            actionType: '',

            formId: '',
            jobId: '',
            nodeId: '',
            workflowId: '',

            jobList: [],

            nodeTitle: '',

            reviewUserList: [
                { uid: '11047ab0-c5ed-11ec-bd73-036750df1a60', name: 'GWP' },
                { uid: 'dbfe84b4-6a80-11ea-a11d-27a6607da11a', name: 'LWQ' },
                { uid: 'd65fa2b4-48be-11eb-8f84-7b9620caa627', name: 'XB' },
                { uid: 'f9132d20-c5ec-11ec-bd73-036750df1a60', name: 'WW' },
                { uid: '901108c0-64bf-11ed-b5e4-4788e007772f', name: 'LWL' },
                { uid: '', name: 'ZL' },
            ],

            userId: JSON.parse(
                localStorage.getItem(`${AppConfig.sys_code_prefix}-userInfo`)
            ).id,
            userName: JSON.parse(
                localStorage.getItem(`${AppConfig.sys_code_prefix}-userInfo`)
            ).fullName,
        };
    },
    methods: {
        getMainFileInfo(filesList) {
            this.formData.mainFiles = filesList;
        },
        getAttachFileInfo(filesList) {
            this.formData.attachFiles = filesList;
        },
        getChipValue(chip) {
            this.reviewComment = chip;
        },

        async initHtmlForm() {
            if (this.$route.query.workflowId) {
                this.jobId = this.$route.query.jobId;
                this.nodeId = this.$route.query.nodeId;
                this.workflowId = this.$route.query.workflowId;

                const initInfo = await initForm(
                    this.$axios,
                    this.jobId,
                    this.nodeId,
                    this.workflowId
                );

                this.actionType = initInfo.actionType;
                this.docNumber = initInfo.docNumber;
                this.docType = initInfo.docType;
                this.formData = initInfo.formData;
                this.formId = initInfo.formId;
                this.jobList = initInfo.jobList;
                this.nodePermission = initInfo.nodePermission;

                this.editAble = isEditable(this.jobList, this.userId);
                if (!this.editAble) {
                    return;
                }

                const { nodeTitle } = initInfo;

                this.nodeTitle = nodeTitle;

                if (nodeTitle === '发起') {
                    return;
                }
                if (nodeTitle === '文印员') {
                    this.editAble = false;
                    return;
                }

                const currentUser = this.reviewUserList.filter((item) => {
                    return item.uid === this.userId;
                });

                if (currentUser.length === 0) {
                    if (
                        this.formData.mainOrganizers[0]?.name ===
                            this.userName &&
                        this.formData.mainOrganizers[0]?.node === nodeTitle
                    ) {
                        return;
                    }

                    this.formData.mainOrganizers.unshift({
                        name: this.userName,
                        content: '',
                        node: nodeTitle,
                    });
                    return;
                }
                [this.currentUser] = currentUser;

                if (this.formData[currentUser.name][0].node !== nodeTitle) {
                    this.formData[currentUser.name].unshift({
                        node: nodeTitle,
                        content: '',
                    });
                    return;
                }
            }
            this.formData.promoter = this.userName;
            this.formData.tel = JSON.parse(
                localStorage.getItem(`${AppConfig.sys_code_prefix}-userInfo`)
            ).mobile;
            this.editAble = true;
        },
    },

    mounted() {
        this.initHtmlForm();
    },

    watch: {
        reviewComment(newValue) {
            if (
                this.formData[this.currentUser.name][0].node === this.nodeTitle
            ) {
                this.formData[
                    this.currentUser.name
                ][0].content = `${newValue} ${now()}`;
                return;
            }
            this.formData.mainOrganizers[0].content = `${newValue} ${now()}`;
        },
    },
};
</script>

<style scoped>
span {
    color: black;
    word-wrap: anywhere;
}

.borderR {
    border-right: red 1px solid;
    flex: 1;
}

.borderB {
    border-bottom: red 1px solid;
}
</style>
