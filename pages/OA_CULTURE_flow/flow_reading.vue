<template>
    <v-container fluid>
        <v-row>
            <v-col
                cols="12"
                lg="8"
                style="margin: 0 auto; max-width: 960px"
                xs="12"
            >
                <v-card class="pa-4">
                    <v-card-title>
                        <v-btn
                            color="primary"
                            icon
                            small
                            @click="$router.go(-1)"
                        >
                            <v-icon>mdi-arrow-left</v-icon>
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                            v-print="'#printForm'"
                            color="primary"
                            icon
                            small
                        >
                            <v-icon>mdi-printer</v-icon>
                        </v-btn>
                    </v-card-title>
                    <v-card-text>
                        <form
                            id="printForm"
                            style="line-height: 32px; color: black"
                        >
                            <h1 style="text-align: center; margin-bottom: 32px">
                                普发阅件
                            </h1>

                            <div
                                style="
                                    display: grid;
                                    grid-template-columns: repeat(4, 1fr);
                                    border: black 1px solid;
                                    background-color: black;
                                    gap: 1px;
                                    -webkit-print-color-adjust: exact;
                                "
                            >
                                <div><span>编号</span></div>
                                <div class="box3">
                                    <span>{{ formData.number }}</span>
                                </div>
                                <div><span>时间</span></div>
                                <div class="box3">
                                    <span>{{ formData.date }}</span>
                                </div>

                                <div><span>文件名称</span></div>
                                <div class="box3">
                                    <span>{{ formData.fileName }}</span>
                                </div>

                                <div><span>领导签批</span></div>
                                <div class="box3">
                                    <div
                                        v-for="item in formData.header"
                                        :key="item.index"
                                        style="text-align: left"
                                    >
                                        <span>
                                            {{ item.name }}:
                                            {{ item.content }}</span
                                        >
                                    </div>
                                </div>
                            </div>
                        </form>
                    </v-card-text>
                </v-card>
            </v-col>

            <v-col cols="12" lg="4" xs="12">
                <v-card class="pa-4">
                    <v-tabs>
                        <v-tab @click="tab = 0">内容</v-tab>
                        <v-tab @click="tab = 1">时间线</v-tab>
                    </v-tabs>
                    <v-card-text v-show="tab === 0">
                        <v-form
                            v-if="
                                !$route.query.workflowId || jobList.length <= 1
                            "
                        >
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.number"
                                        :rules="[rules.required]"
                                        dense
                                        label="编号"
                                    />
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.fileName"
                                        :rules="[rules.required]"
                                        dense
                                        label="文件名称"
                                    />
                                </v-col>
                            </v-row>
                        </v-form>
                        <v-form
                            v-if="
                                $route.query.workflowId &&
                                editAble &&
                                jobList.length > 1
                            "
                        >
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="reviewComment"
                                        :rules="[rules.required]"
                                        dense
                                        label="请输入批示"
                                    />
                                </v-col>
                            </v-row>
                            <ReviewChips :getChipValue="getChipValue" />
                        </v-form>
                        <v-row>
                            <v-col>
                                正文：
                                <FileUploadV1
                                    :allowUpload="
                                        jobList.length <= 1
                                            ? true
                                            : nodePermission.fileModify
                                    "
                                    :filesProps="formData.mainFiles"
                                    :getFileInfo="getMainFileInfo"
                                />
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                附件：
                                <FileUploadV1
                                    :allowUpload="
                                        jobList.length <= 1
                                            ? true
                                            : nodePermission.fileModify
                                    "
                                    :filesProps="formData.attachFiles"
                                    :getFileInfo="getAttachFileInfo"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>

                    <v-card-actions
                        v-if="tab === 0 && editAble && jobList.length <= 1"
                    >
                        <FlowCreateButton
                            :formData="formData"
                            :formIdProp="formId"
                            :jobIdProp="jobId"
                            :nodeIdProp="nodeId"
                            :workflowIdProp="workflowId"
                        />
                    </v-card-actions>

                    <v-card-actions
                        v-if="tab === 0 && editAble && jobList.length > 1"
                    >
                        <StagingButton
                            :currentComment="reviewComment"
                            :formData="formData"
                            :formId="formId"
                            :jobId="jobId"
                        />
                        <SubmitButton
                            v-if="actionType !== '2'"
                            :currentComment="reviewComment"
                            :formData="formData"
                            :formId="formId"
                            :jobId="jobId"
                            :nodeId="nodeId"
                            :workflowId="workflowId"
                        />
                        <CountersignButton
                            v-if="
                                actionType !== '2' &&
                                nodePermission.countersignCreate
                            "
                            :currentComment="reviewComment"
                            :formData="formData"
                            :formId="formId"
                            :jobId="jobId"
                            :nodeId="nodeId"
                            :workflowId="workflowId"
                        />
                        <ReadButton
                            v-if="nodePermission.readCreate"
                            :currentComment="reviewComment"
                            :nodeId="nodeId"
                            :workflowId="workflowId"
                        />
                        <TransferButton
                            v-if="nodePermission.transferCreate"
                            :formId="formId"
                            :nodeId="nodeId"
                            :workflowId="workflowId"
                        />
                        <CountersigningButton
                            v-if="actionType === '2'"
                            :currentComment="reviewComment"
                            :formData="formData"
                            :formId="formId"
                            :jobId="jobId"
                        />
                        <BackingButton
                            v-if="nodePermission.returnCreate"
                            :formId="formId"
                            :nodeId="nodeId"
                            :workflowId="workflowId"
                        />
                    </v-card-actions>

                    <v-card-text v-if="tab === 1">
                        <TimelineCard
                            :jobList="jobList"
                            :workflowId="workflowId"
                        />
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import print from 'vue-print-nb';
import AppConfig from '../../constants/AppConfig';
import initForm from '../../helper/initForm';
import isEditable from '../../helper/isEditable';

import ReviewChips from '../../components/formFlow/formComponent/ReviewChips.vue';
import FileUploadV1 from '../../components/FileUploadV1.vue';

import FlowCreateButton from '../../components/formFlow/formFunction/FormCreate.vue';
import StagingButton from '../../components/formFlow/formFunction/StagingCreate.vue';
import SubmitButton from '../../components/formFlow/formFunction/FormSubmit.vue';
import CountersignButton from '../../components/formFlow/formFunction/CountersignCreate.vue';
import ReadButton from '../../components/formFlow/formFunction/ReadCreate.vue';
import TransferButton from '../../components/formFlow/formFunction/TransferCreate.vue';
import BackingButton from '../../components/formFlow/formFunction/BackingCreate.vue';
import CountersigningButton from '../../components/formFlow/formFunction/CountersignEnd.vue';

import TimelineCard from '../../components/formFlow/TimelineCard.vue';

export default {
    name: 'flow_reading',
    directives: { print },
    components: {
        ReviewChips,
        FileUploadV1,
        FlowCreateButton,
        StagingButton,
        SubmitButton,
        CountersignButton,
        ReadButton,
        TransferButton,
        BackingButton,
        CountersigningButton,
        TimelineCard,
    },
    data() {
        return {
            editAble: false,
            nodePermission: {},
            formData: {
                title: '',

                number: '',
                date: new Date(Date.now())
                    .toLocaleDateString('zh-CN')
                    .replace(/\//g, '-'),
                fileName: '',
                header: [],

                mainFiles: '',
                attachFiles: '',
            },

            tab: 0,

            rules: {
                required: (value) => !!value || '此条目不能为空',
            },

            reviewComment: '',

            actionType: '',

            formId: '',
            jobId: '',
            nodeId: '',
            workflowId: '',

            jobList: [],

            userId: JSON.parse(
                localStorage.getItem(`${AppConfig.sys_code_prefix}-userInfo`)
            ).id,
            userName: JSON.parse(
                localStorage.getItem(`${AppConfig.sys_code_prefix}-userInfo`)
            ).fullName,
        };
    },
    methods: {
        getMainFileInfo(filesList) {
            this.formData.mainFiles = filesList;
        },
        getAttachFileInfo(filesList) {
            this.formData.attachFiles = filesList;
        },
        getChipValue(chip) {
            this.reviewComment = chip;
        },

        async initHtmlForm() {
            if (this.$route.query.workflowId) {
                this.jobId = this.$route.query.jobId;
                this.nodeId = this.$route.query.nodeId;
                this.workflowId = this.$route.query.workflowId;

                const initInfo = await initForm(
                    this.$axios,
                    this.jobId,
                    this.nodeId,
                    this.workflowId
                );

                this.actionType = initInfo.actionType;
                this.formData = initInfo.formData;
                this.formId = initInfo.formId;
                this.jobList = initInfo.jobList;
                this.nodePermission = initInfo.nodePermission;

                this.editAble = isEditable(this.jobList, this.userId);
                if (!this.editAble) {
                    return;
                }

                const { nodeTitle } = initInfo;
                if (nodeTitle === '文印员') {
                    this.editAble = false;
                    return;
                }

                if (
                    this.formData.header[0].name === this.userName &&
                    this.formData.header[0].node === nodeTitle
                ) {
                    return;
                }

                this.formData.header.unshift({
                    name: this.userName,
                    content: '',
                    node: nodeTitle,
                });
            }
            this.formData.promoter = this.userName;
            this.formData.title = '普发阅件';
            this.editAble = true;
        },
    },

    mounted() {
        this.initHtmlForm();
    },

    watch: {
        reviewComment(newValue) {
            this.formData.header[0].content = `${newValue} ${now()}`;
        },
    },
};
</script>

<style scoped>
div {
    background-color: white;
    text-align: center;
}

span {
    word-wrap: anywhere;
}

.box3 {
    grid-column: span 3 / span 3;
}
</style>
