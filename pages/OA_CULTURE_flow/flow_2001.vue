<template>
    <v-container fluid>
        <v-row>
            <v-col
                cols="12"
                lg="8"
                style="margin: 0 auto; max-width: 960px"
                xs="12"
            >
                <v-card class="pa-4">
                    <v-card-title>
                        <v-btn
                            color="primary"
                            icon
                            small
                            @click="$router.go(-1)"
                        >
                            <v-icon>mdi-arrow-left</v-icon>
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                            v-print="'#printForm'"
                            color="primary"
                            icon
                            small
                        >
                            <v-icon>mdi-printer</v-icon>
                        </v-btn>
                    </v-card-title>
                    <v-card-text>
                        <form
                            id="printForm"
                            style="
                                color: red;
                                line-height: 24px;
                                display: grid;
                                grid-template-columns: 1fr;
                                gap: 1px;
                                background: red;
                            "
                        >
                            <div>
                                <h1
                                    style="
                                        text-align: center;
                                        margin-bottom: 24px;
                                    "
                                >
                                    呈&nbsp;&nbsp;&nbsp;&nbsp;批&nbsp;&nbsp;&nbsp;&nbsp;件
                                </h1>

                                <div>
                                    保利文化集团股份有限公司党委
                                    <div
                                        v-if="
                                            formInfo.docType &&
                                            formInfo.docNumber
                                        "
                                        style="float: right"
                                    >
                                        [
                                        <span>{{
                                            formInfo.formCreatedAt.slice(0, 4)
                                        }}</span>
                                        ] 呈
                                        <span>{{ formInfo.docType }}</span>
                                        字 第
                                        <span>{{ formInfo.docNumber }} </span>号
                                    </div>
                                </div>
                            </div>

                            <div>
                                题目：
                                <span>{{ formData.title }} </span>
                            </div>

                            <div style="min-height: 64px">
                                <div>{{ reviewUserList[0].name }}批示:</div>
                                <div
                                    v-for="comment in formData[
                                        reviewUserList[0].pinyin
                                    ]"
                                    :key="comment.index"
                                >
                                    <span>{{ comment.content }}</span>
                                </div>
                            </div>

                            <div
                                style="
                                    display: grid;
                                    grid-template-columns: repeat(2, 1fr);
                                    gap: 1px;
                                    background-color: red;
                                "
                            >
                                <div
                                    v-for="user in reviewUserList.slice(1, 9)"
                                    :key="user.uid"
                                    style="min-height: 64px"
                                >
                                    <div>{{ user.name }}批示:</div>
                                    <div
                                        v-for="comment in formData[user.pinyin]"
                                        :key="comment.index"
                                    >
                                        <span>{{ comment.content }}</span>
                                    </div>
                                </div>
                            </div>

                            <div style="min-height: 64px">
                                <div>主办部门意见：</div>
                                <div
                                    v-for="item in formData.mainOrganizers"
                                    :key="item.index"
                                >
                                    <span
                                        >{{ item.name }}:{{
                                            item.content
                                        }}</span
                                    >
                                </div>
                            </div>

                            <div style="display: flex">
                                <div style="flex: 1">
                                    承办人：
                                    <span>{{ formData.promoter }}</span>
                                </div>
                                <div style="flex: 1">
                                    电话：
                                    <span>{{ formData.tel }}</span>
                                </div>
                            </div>
                        </form>
                    </v-card-text>
                </v-card>
            </v-col>

            <v-col cols="12" lg="4" xs="12">
                <v-card class="pa-4">
                    <v-tabs>
                        <v-tab @click="isShowTimeLine = false">内容</v-tab>
                        <v-tab @click="isShowTimeLine = true">时间线</v-tab>
                    </v-tabs>
                    <v-card-text v-show="!isShowTimeLine">
                        <v-form v-if="jobInfo.isCreating">
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.title"
                                        :rules="[rules.required]"
                                        dense
                                        label="题目"
                                    />
                                </v-col>
                            </v-row>
                        </v-form>
                        <v-form
                            v-if="
                                isEditable &&
                                (jobInfo.isReviewing || jobInfo.isSigning) &&
                                !jobInfo.isEnding
                            "
                        >
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="currentComment"
                                        :rules="[rules.required]"
                                        dense
                                        label="请输入批示"
                                    />
                                </v-col>
                            </v-row>
                            <ReviewChips :getChipValue="getChipValue" />
                        </v-form>
                        <v-row>
                            <v-col>
                                正文：
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.mainFiles"
                                    :getFileInfo="getMainFileInfo"
                                />
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                附件：
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.attachFiles"
                                    :getFileInfo="getAttachFileInfo"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>
                    <ActionButtons
                        v-if="isShowFunctionArea"
                        :currentComment="currentComment"
                        :currentCommentField="currentCommentField"
                        :formInfo="formInfo"
                        :isEditable="isEditable"
                        :isShowTimeLine="isShowTimeLine"
                        :jobInfo="jobInfo"
                        :nodeInfo="nodeInfo"
                        :workflowId="workflowId"
                    />
                    <v-card-text v-if="isShowTimeLine">
                        <TimelineCard
                            :jobList="jobInfo.jobList"
                            :nodeList="nodeInfo.nodeList"
                        />
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import print from 'vue-print-nb';
import FileUploadV1 from '../../components/FileUploadV1.vue';
import TimelineCard from '../../components/formFlow/TimelineCard.vue';
import { localUserInfo } from '../../helper/localUserInfo';

import initForm from '../../helper/initForm';
import isEditable from '../../helper/isEditable';

import ReviewChips from '../../components/formFlow/formComponent/ReviewChips.vue';
import ActionButtons from '../../components/formFlow/ActionButtons.vue';

export default {
    name: 'flow_2001',
    components: {
        FileUploadV1,
        TimelineCard,
        ReviewChips,
        ActionButtons,
    },
    directives: { print },
    data() {
        return {
            isShowFunctionArea: false,
            isEditable: false,
            isShowTimeLine: false,

            jobId: '',
            nodeId: '',
            workflowId: '',

            formInfo: {},
            jobInfo: {},
            nodeInfo: {},

            currentComment: '',
            currentCommentField: '',

            formData: {
                title: '',
                promoter: '',
                tel: '',

                WB: [],
                GWP: [],
                XB: [],
                JYC: [],
                LWQ: [],
                WW: [],
                LWL: [],
                ZL: [],
                GJW: [],
                mainOrganizers: [],

                mainFiles: '',
                attachFiles: '',

                reviewUserList: [],
            },

            reviewUserList: [
                {
                    uid: '9008cb60-64bf-11ed-b5e4-4788e007772f',
                    pinyin: 'WB',
                    name: '王\xa0\xa0\xa0\xa0波书记',
                },
                {
                    uid: '11047ab0-c5ed-11ec-bd73-036750df1a60',
                    pinyin: 'GWP',
                    name: '郭文鹏副书记',
                },
                {
                    uid: 'd65fa2b4-48be-11eb-8f84-7b9620caa627',
                    pinyin: 'XB',
                    name: '徐\xa0\xa0\xa0\xa0碚副书记',
                },
                {
                    uid: 'd65fa2b2-48be-11eb-8f84-7b9620caa627',
                    pinyin: 'JYC',
                    name: '蒋迎春委员',
                },
                {
                    uid: 'dbfe84b4-6a80-11ea-a11d-27a6607da11a',
                    pinyin: 'LWQ',
                    name: '李卫强委员',
                },
                {
                    uid: 'f9132d20-c5ec-11ec-bd73-036750df1a60',
                    pinyin: 'WW',
                    name: '王\xa0\xa0\xa0\xa0蔚委员',
                },
                {
                    uid: '901108c0-64bf-11ed-b5e4-4788e007772f',
                    pinyin: 'LWL',
                    name: '李文亮委员',
                },
                {
                    uid: 'd55cb1a2-48be-11eb-8f84-7b9620caa627',
                    pinyin: 'ZL',
                    name: '赵\xa0\xa0\xa0\xa0琳委员',
                },
                {
                    uid: '90121a30-64bf-11ed-b5e4-4788e007772f',
                    pinyin: 'GJW',
                    name: '郭建巍委员',
                },
            ],

            userId: localUserInfo.id,
            userName: localUserInfo.fullName,

            rules: {
                required: (value) => !!value || '此条目不能为空',
            },
        };
    },
    methods: {
        getMainFileInfo(filesList) {
            this.formData.mainFiles = filesList;
        },

        getAttachFileInfo(filesList) {
            this.formData.attachFiles = filesList;
        },

        getChipValue(chip) {
            this.currentComment = chip;
        },

        async initHtmlForm() {
            if (this.$route.query.workflowId) {
                this.workflowId = this.$route.query.workflowId;
                this.nodeId = this.$route.query.nodeId;
                this.jobId = this.$route.query.jobId;

                const initInfo = await initForm(
                    this.$axios,
                    this.jobId,
                    this.nodeId,
                    this.workflowId
                );

                this.formInfo = initInfo.formInfo;
                this.jobInfo = initInfo.jobInfo;
                this.nodeInfo = initInfo.nodeInfo;
                this.formData = initInfo.formInfo.formData;
                this.reviewUserList = this.formData.reviewUserList;

                this.isEditable = isEditable(this.jobInfo.jobList, this.userId);
                this.isShowFunctionArea = true;

                if (!this.isEditable) return;
                if (this.jobInfo.isEnding) return;
                if (this.jobInfo.isCreating) return;

                if (
                    this.jobInfo.isJointDrafting &&
                    !this.formData.promoter.includes('、')
                ) {
                    this.formData.promoter = `${this.formData.promoter}、${this.userName}`;
                    return;
                }

                const userOnForm = this.formData.reviewUserList.find(
                    (item) => item.uid === this.userId
                );

                this.currentCommentField = userOnForm
                    ? userOnForm.pinyin
                    : 'mainOrganizers';

                if (!this.jobInfo.isStaging) {
                    this.formData[this.currentCommentField].unshift({
                        name: this.userName,
                        content: '',
                    });
                }

                return;
            }

            this.formData.promoter = this.userName;
            this.formData.tel = localUserInfo.mobile;
            this.formData.reviewUserList = this.reviewUserList;
            this.formInfo.formData = this.formData;
            this.jobInfo.isCreating = true;
            this.isEditable = true;
            this.isShowFunctionArea = true;
        },
    },
    mounted() {
        this.initHtmlForm();
    },
    watch: {
        currentComment(newValue) {
            this.formData[this.currentCommentField][0].content = newValue;
        },
    },
};
</script>

<style scoped>
div {
    background-color: white;
}

span {
    color: black;
}
</style>
