<template>
    <v-container fluid>
        <v-card>
            <v-card-text>
                <v-row>
                    <v-col>
                        <img
                            alt="userProfileBanner"
                            src="/userProfile/profile-banner.jpg"
                            style="max-width: 100%"
                        />
                    </v-col>
                </v-row>
                <v-row>
                    <v-col class="text-center">
                        <v-avatar size="108">
                            <img
                                :src="userInfo?.avatar"
                                alt="userAvatar"
                                style="width: 100%; height: auto"
                            />
                        </v-avatar>
                    </v-col>
                </v-row>
                <v-row>
                    <v-col class="text-center">{{ userInfo?.fullName }}</v-col>
                </v-row>
                <v-row>
                    <v-col class="text-right">Email</v-col>
                    <v-col>{{ userInfo?.email }}</v-col>
                </v-row>
                <v-row>
                    <v-col class="text-right">手机</v-col>
                    <v-col>{{ userInfo?.mobile }}</v-col>
                </v-row>
                <v-row>
                    <v-col class="text-right">用户名</v-col>
                    <v-col>{{ userInfo?.username }}</v-col>
                </v-row>
                <v-row>
                    <v-col class="text-right">公司</v-col>
                    <v-col>{{ userInfo?.company?.name }}</v-col>
                </v-row>
                <v-row>
                    <v-col class="text-right">部门</v-col>
                    <v-col>{{ userInfo?.department?.name }}</v-col>
                </v-row>
                <v-row>
                    <v-col class="text-center">
                        <v-btn
                            color="primary"
                            small
                            @click="changePWDDialog = true"
                            >修改密码
                        </v-btn>
                    </v-col>
                </v-row>
            </v-card-text>
        </v-card>
        <v-dialog v-model="changePWDDialog" max-width="600px">
            <v-card>
                <v-card-title>修改密码</v-card-title>
                <v-card-text>
                    <v-form ref="changePWDForm">
                        <v-row>
                            <v-col>
                                <v-text-field
                                    v-model="changePWDForm.old_password"
                                    :rules="[rules.required]"
                                    label="原密码"
                                    type="password"
                                ></v-text-field>
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                <v-text-field
                                    v-model="changePWDForm.password"
                                    :rules="[rules.required, rules.password]"
                                    label="新密码"
                                    type="password"
                                ></v-text-field>
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                <v-text-field
                                    v-model="changePWDForm.copy_password"
                                    :rules="[
                                        rules.required,
                                        rules.reEnterPassword,
                                    ]"
                                    label="再次输入新密码"
                                    type="password"
                                ></v-text-field>
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col cols="8">
                                <v-text-field
                                    :value="userInfo.mobile"
                                    label="手机号"
                                    readonly
                                ></v-text-field>
                            </v-col>
                            <v-col class="" cols="4">
                                <v-btn
                                    :disabled="sendSMSCodeButtonDisabled"
                                    color="primary"
                                    @click="sendSMSCode"
                                    >发送短信验证码
                                </v-btn>
                                <div v-show="SMSInterval !== 60">
                                    {{ SMSInterval }}秒后可重新发送
                                </div>
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                <v-text-field
                                    v-model="changePWDForm.vertify_code"
                                    :rules="[rules.required]"
                                    label="短信验证码"
                                    type="number"
                                ></v-text-field>
                            </v-col>
                        </v-row>
                    </v-form>
                    <v-card-actions>
                        <v-row>
                            <v-col class="text-right">
                                <v-btn
                                    class="ml-1"
                                    color="primary"
                                    small
                                    @click="submitNewPWD"
                                >
                                    确定修改
                                </v-btn>
                                <v-btn
                                    class="ml-1"
                                    small
                                    @click="changePWDDialog = false"
                                    >取消
                                </v-btn>
                            </v-col>
                        </v-row>
                    </v-card-actions>
                </v-card-text>
            </v-card>
        </v-dialog>
    </v-container>
</template>

<script>
import AppConfig from '../constants/AppConfig';

export default {
    name: 'userProfile',
    data() {
        return {
            rules: {
                required: (value) => !!value || '此条目不能为空',
                password: (value) => value.length >= 8 || '密码长度8位以上',
                reEnterPassword: (value) =>
                    value === this.changePWDForm.password || '两次密码不一致',
            },

            changePWDDialog: false,

            userInfo: JSON.parse(
                localStorage.getItem(`${AppConfig.sys_code_prefix}-userInfo`)
            ),

            sendSMSCodeButtonDisabled: false,
            SMSInterval: 60,

            changePWDForm: {
                old_password: '',
                password: '',
                copy_password: '',
                vertify_code: '',
            },
        };
    },
    methods: {
        async sendSMSCode() {
            const result = await this.$axios.post('/auth/uc/vertifycode/send');
            if (result.state === 'error') {
                alert(result.errorMsg);
                return;
            }

            this.sendSMSCodeButtonDisabled = true;

            const timeInterval = setInterval(() => {
                this.SMSInterval -= 1;
                if (this.SMSInterval === 0) {
                    clearInterval(timeInterval);
                    this.SMSInterval = 60;
                    this.sendSMSCodeButtonDisabled = false;
                }
            }, 1000);
        },

        async submitNewPWD() {
            if (!this.$refs.changePWDForm.validate()) {
                return;
            }

            const result = await this.$axios.post(
                '/auth/uc/modifypwd',
                this.changePWDForm
            );
            if (result.state === 'error') {
                alert(result.errorMsg);
                return;
            }

            localStorage.clear();

            this.$router.push('/');
        },
    },
};
</script>

<style scoped></style>
