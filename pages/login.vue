<template>
    <div class="user-center-login">
        <div v-if="loading">
            <div class="flip-square" />
            <div class="flip-info">登录中...</div>
        </div>
        <div v-else>
            <v-alert
                border="right"
                class="alert-error"
                colored-border
                elevation="2"
                type="error"
            >
                {{ errorMsg }}
            </v-alert>
        </div>
    </div>
</template>

<script>
import AppConfig from '../constants/AppConfig';

export default {
    // eslint-disable-next-line vue/multi-word-component-names
    name: 'login',
    data() {
        return {
            errorMsg: '',
            loading: false,
        };
    },
    methods: {
        async getUserInfo(code) {
            const userResult = await this.$axios.post(`/auth/uc/current/user`, {
                code,
            });
            if (userResult.state !== 'ok') {
                this.errorMsg = userResult.message;
                this.loading = false;
                return;
            }
            await localStorage.setItem(
                `${AppConfig.sys_code_prefix}-tokenValue`,
                userResult.token
            );
            await localStorage.setItem(
                `${AppConfig.sys_code_prefix}-userInfo`,
                JSON.stringify(userResult.user)
            );

            const roleResult = await this.$axios.get('/api/be/oa/user/myrole');
            if (roleResult.errMessage) {
                this.errorMsg = userResult.errMessage;
                this.loading = false;
                return;
            }
            await localStorage.setItem(
                `${AppConfig.sys_code_prefix}-oa_role`,
                roleResult.role
            );

            await this.$router.push('/');
        },
    },

    created() {
        const { code } = this.$router.history.current.query;
        if (code) {
            this.loading = true;
            this.getUserInfo(code);
        } else {
            this.errorMsg = '数据错误，请联系管理员';
        }
    },

    beforeDestroy() {
        this.loading = false;
    },
};
</script>

<style scoped>
.user-center-login {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.flip-square:before {
    -webkit-animation: flip-square 1.5s infinite;
    animation: flip-square 1.5s infinite;
    background-color: #33b1ff;
    content: '';
    display: block;
    height: 50px;
    width: 50px;
}

.flip-info {
    margin-top: 20px;
    font-weight: 600;
    font-size: 20px;
}

@-webkit-keyframes flip-square {
    0% {
        -webkit-transform: perspective(50px) rotate(45deg) rotateX(0deg)
            rotateY(0deg);
        transform: perspective(50px) rotate(45deg) rotateX(0deg) rotateY(0deg);
    }
    50% {
        -webkit-transform: perspective(50px) rotate(45deg) rotateX(-180deg)
            rotateY(0deg);
        transform: perspective(50px) rotate(45deg) rotateX(-180deg)
            rotateY(0deg);
    }
    100% {
        -webkit-transform: perspective(50px) rotate(45deg) rotateX(-180deg)
            rotateY(-180deg);
        transform: perspective(50px) rotate(45deg) rotateX(-180deg)
            rotateY(-180deg);
    }
}

@keyframes flip-square {
    0% {
        -webkit-transform: perspective(50px) rotate(45deg) rotateX(0deg)
            rotateY(0deg);
        transform: perspective(50px) rotate(45deg) rotateX(0deg) rotateY(0deg);
    }
    50% {
        -webkit-transform: perspective(50px) rotate(45deg) rotateX(-180deg)
            rotateY(0deg);
        transform: perspective(50px) rotate(45deg) rotateX(-180deg)
            rotateY(0deg);
    }
    100% {
        -webkit-transform: perspective(50px) rotate(45deg) rotateX(-180deg)
            rotateY(-180deg);
        transform: perspective(50px) rotate(45deg) rotateX(-180deg)
            rotateY(-180deg);
    }
}

.alert-error {
    min-width: 20rem;
}
</style>
