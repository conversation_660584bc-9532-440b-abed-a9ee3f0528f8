<template>
    <v-container fluid>
        <v-row>
            <v-col>
                <v-btn color="primary" icon large @click="$router.go(-1)">
                    <v-icon>mdi-arrow-left</v-icon>
                </v-btn>
            </v-col>
        </v-row>
        <v-row>
            <v-col>
                <v-card>
                    <v-card-title>
                        <span style="margin: 0 auto"> {{ title }}</span>
                    </v-card-title>
                    <v-card-text>
                        <VueEditor
                            v-if="content"
                            v-model="content"
                            :editorToolbar="[[]]"
                            disabled
                        />
                        <FileUploadV1
                            :allowUpload="false"
                            :filesProps="attachFiles"
                        />
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import FileUploadV1 from '../components/FileUploadV1.vue';

export default {
    name: 'notificationDetail',
    components: { FileUploadV1 },
    data() {
        return {
            title: '',
            content: '',
            attachFiles: '',
        };
    },
    methods: {
        async getInfo() {
            const result = await this.$axios.get(
                `/api/be/oa/article/info?id=${this.$route.query.id}`
            );
            if (result.state === 'error') {
                alert(result.errorMsg);
                return;
            }

            this.title = result.title;
            this.content = result.content;
            this.attachFiles = result.attach_files;
        },
    },
    mounted() {
        this.getInfo();
    },
};
</script>

<style scoped></style>
