<template>
    <v-container fluid>
        <v-row>
            <v-col
                clos="12"
                lg="8"
                style="margin: 0 auto; max-width: 960px"
                xs="12"
            >
                <v-card class="pa-4">
                    <v-card-title>
                        <v-btn
                            color="primary"
                            icon
                            small
                            @click="$router.go(-1)"
                        >
                            <v-icon>mdi-arrow-left</v-icon>
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                            v-print="'#printForm'"
                            color="primary"
                            icon
                            small
                        >
                            <v-icon>mdi-printer</v-icon>
                        </v-btn>
                    </v-card-title>
                    <v-card-text>
                        <form
                            id="printForm"
                            style="
                                color: black;
                                line-height: 32px;
                                text-align: center;
                            "
                        >
                            <h1>北京新保利大厦房地产开发有限公司</h1>
                            <h2>职工年休假审批表</h2>

                            <div
                                style="
                                    display: grid;
                                    grid-template-columns: repeat(4, 1fr);
                                    gap: 1px;
                                    background-color: black;
                                    border: solid 1px black;
                                "
                            >
                                <div>部门</div>
                                <div>{{ formData.department }}</div>
                                <div>姓名</div>
                                <div>{{ formData.name }}</div>

                                <div>参加工作时间</div>
                                <div>{{ formData.workingTime }}</div>
                                <div>规定休假天数</div>
                                <div>{{ formData.requiredTime }}</div>

                                <div>联系电话</div>
                                <div>{{ formData.tel }}</div>
                                <div>已休假天数</div>
                                <div>{{ formData.alreadyVacation }}</div>

                                <div>离岗时间</div>
                                <div>{{ formData.leaveTime }}</div>
                                <div>本次请假天数</div>
                                <div>{{ formData.vacationDays }}</div>

                                <div>休假地点</div>
                                <div>{{ formData.vacationZones }}</div>
                                <div>剩余休假天数</div>
                                <div>{{ formData.remainingVacationDays }}</div>

                                <div class="grid1_3">
                                    <div>部门领导意见</div>
                                    <div style="grid-column: span 3">
                                        <div style="min-height: 32px">
                                            <div
                                                v-for="item in formData.departmentHeader"
                                                :key="item.index"
                                            >
                                                <span
                                                    >{{ item.name }}：{{
                                                        item.content
                                                    }}</span
                                                >
                                            </div>
                                        </div>
                                        <div style="text-align: right">
                                            签字：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                            &nbsp;&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;日
                                        </div>
                                    </div>
                                </div>

                                <div class="grid1_3">
                                    <div>公司领导审批意见</div>
                                    <div style="grid-column: span 3">
                                        <div style="min-height: 32px">
                                            <div
                                                v-for="item in formData.companyHeader"
                                                :key="item.index"
                                            >
                                                <span
                                                    >{{ item.name }}：{{
                                                        item.content
                                                    }}</span
                                                >
                                            </div>
                                        </div>
                                        <div style="text-align: right">
                                            签字：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                            &nbsp;&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;日
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </v-card-text>
                </v-card>
            </v-col>

            <v-col cols="12" lg="4" xs="12">
                <v-card class="pa-4">
                    <v-tabs>
                        <v-tab @click="isShowTimeLine = false">内容</v-tab>
                        <v-tab @click="isShowTimeLine = true">时间线</v-tab>
                    </v-tabs>
                    <v-card-text v-show="!isShowTimeLine">
                        <v-form v-if="jobInfo.isCreating">
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.department"
                                        :rules="[rules.required]"
                                        dense
                                        label="部门"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.name"
                                        :rules="[rules.required]"
                                        dense
                                        label="姓名"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.workingTime"
                                        :rules="[rules.required]"
                                        dense
                                        label="参加工作时间"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.requiredTime"
                                        :rules="[rules.required]"
                                        dense
                                        label="规定休假天数"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.tel"
                                        :rules="[rules.required]"
                                        dense
                                        label="联系电话"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.alreadyVacation"
                                        :rules="[rules.required]"
                                        dense
                                        label="已休假天数"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.leaveTime"
                                        :rules="[rules.required]"
                                        dense
                                        label="离岗时间"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.vacationDays"
                                        :rules="[rules.required]"
                                        dense
                                        label="本次请假天数"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.vacationZones"
                                        :rules="[rules.required]"
                                        dense
                                        label="休假地点"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.remainingVacationDays"
                                        :rules="[rules.required]"
                                        dense
                                        label="剩余休假天数"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                        </v-form>
                        <v-form
                            v-if="
                                isEditable &&
                                (jobInfo.isReviewing || jobInfo.isSigning) &&
                                !jobInfo.isEnding
                            "
                        >
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="currentComment"
                                        dense
                                        label="请输入批示"
                                    ></v-text-field>
                                </v-col>
                            </v-row>
                            <ReviewChips :getChipValue="getChipValue" />
                        </v-form>
                        <v-row>
                            <v-col>
                                正文：
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.mainFiles"
                                    :getFileInfo="getMainFileInfo"
                                />
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                附件：
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.attachFiles"
                                    :getFileInfo="getAttachFileInfo"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>

                    <ActionButtons
                        v-if="isShowFunctionArea"
                        :currentComment="currentComment"
                        :currentCommentField="currentCommentField"
                        :formInfo="formInfo"
                        :isEditable="isEditable"
                        :isShowTimeLine="isShowTimeLine"
                        :jobInfo="jobInfo"
                        :nodeInfo="nodeInfo"
                        :workflowId="workflowId"
                    />
                    <v-card-text v-if="isShowTimeLine">
                        <TimelineCard
                            :jobList="jobInfo.jobList"
                            :nodeList="nodeInfo.nodeList"
                        />
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>
<script>
import print from 'vue-print-nb';
import FileUploadV1 from '../../components/FileUploadV1.vue';
import TimelineCard from '../../components/formFlow/TimelineCard.vue';
import { localUserInfo } from '../../helper/localUserInfo';

import initForm from '../../helper/initForm';
import isEditable from '../../helper/isEditable';

import ReviewChips from '../../components/formFlow/formComponent/ReviewChips.vue';
import ActionButtons from '../../components/formFlow/ActionButtons.vue';

export default {
    name: 'flow_1005',
    components: {
        FileUploadV1,
        TimelineCard,
        ReviewChips,
        ActionButtons,
    },
    directives: { print },
    data() {
        return {
            isShowFunctionArea: false,
            isEditable: false,
            isShowTimeLine: false,

            jobId: '',
            nodeId: '',
            workflowId: '',

            formInfo: {},
            jobInfo: {},
            nodeInfo: {},

            currentComment: '',
            currentCommentField: '',

            formData: {
                title: '',
                department: '',
                name: '',
                workingTime: '',
                requiredTime: '',
                tel: '',
                alreadyVacation: '',
                leaveTime: '',
                vacationDays: '',
                vacationZones: '',
                remainingVacationDays: '',

                departmentHeader: [],
                companyHeader: [],

                mainFiles: '',
                attachFiles: '',
            },

            userId: localUserInfo.id,
            userName: localUserInfo.fullName,

            rules: {
                required: (value) => !!value || '此条目不能为空',
            },
        };
    },

    methods: {
        getMainFileInfo(filesList) {
            this.formData.mainFiles = filesList;
        },

        getAttachFileInfo(filesList) {
            this.formData.attachFiles = filesList;
        },

        getChipValue(chip) {
            this.currentComment = chip;
        },

        async initHtmlForm() {
            if (this.$route.query.workflowId) {
                this.workflowId = this.$route.query.workflowId;
                this.nodeId = this.$route.query.nodeId;
                this.jobId = this.$route.query.jobId;

                const initInfo = await initForm(
                    this.$axios,
                    this.jobId,
                    this.nodeId,
                    this.workflowId
                );
                this.formInfo = initInfo.formInfo;
                this.jobInfo = initInfo.jobInfo;
                this.nodeInfo = initInfo.nodeInfo;
                this.formData = initInfo.formInfo.formData;

                this.isEditable = isEditable(this.jobInfo.jobList, this.userId);
                this.isShowFunctionArea = true;

                if (!this.isEditable) return;
                if (this.jobInfo.isEnding) return;
                if (this.jobInfo.isCreating) return;

                this.currentCommentField = [
                    '总会计师',
                    '总经理',
                    '副总经理',
                    '总经理助理',
                ].includes(this.nodeInfo.nodeTitle)
                    ? 'companyHeader'
                    : 'departmentHeader';

                if (!this.jobInfo.isStaging) {
                    this.formData[this.currentCommentField].unshift({
                        name: this.userName,
                        content: '',
                    });
                }

                return;
            }

            this.formData.title = `${this.userName}的请假申请`;
            this.formInfo.formData = this.formData;
            this.jobInfo.isCreating = true;
            this.isEditable = true;
            this.isShowFunctionArea = true;
        },
    },

    mounted() {
        this.initHtmlForm();
    },

    watch: {
        currentComment(newValue) {
            this.formData[this.currentCommentField][0].content = newValue;
        },
    },
};
</script>
<style scoped>
div {
    background-color: white;
}

.grid1_3 {
    grid-column: span 4;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1px;
    background-color: black;
    min-height: 64px;
}
</style>
