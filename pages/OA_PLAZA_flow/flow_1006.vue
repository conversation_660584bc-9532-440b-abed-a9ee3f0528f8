<template>
    <v-container fluid>
        <v-row>
            <v-col
                clos="12"
                lg="8"
                style="margin: 0 auto; max-width: 960px"
                xs="12"
            >
                <v-card class="pa-4">
                    <v-card-title>
                        <v-btn
                            color="primary"
                            icon
                            small
                            @click="$router.go(-1)"
                        >
                            <v-icon>mdi-arrow-left</v-icon>
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                            v-print="'#printForm'"
                            color="primary"
                            icon
                            small
                        >
                            <v-icon>mdi-printer</v-icon>
                        </v-btn>
                    </v-card-title>
                    <v-card-text>
                        <form
                            id="printForm"
                            style="
                                color: black;
                                text-align: center;
                                line-height: 32px;
                            "
                        >
                            <h1>用印申请表</h1>

                            <div style="text-align: left">
                                填表单位：北京新保利大厦房地产开发有限公司
                            </div>

                            <div
                                style="
                                    color: black;
                                    line-height: 32px;
                                    display: grid;
                                    grid-template-columns: repeat(4, 1fr);
                                    gap: 1px;
                                    background-color: black;
                                    border: solid 1px black;
                                "
                            >
                                <div>用章部门</div>
                                <div style="grid-column: span 3">
                                    {{ formData.sealDepartment }}
                                </div>

                                <div>印章名称</div>
                                <div>{{ formData.sealName }}</div>
                                <div>用章次数</div>
                                <div>{{ formData.sealTimes }}</div>

                                <div>用章事由</div>
                                <div style="grid-column: span 3">
                                    {{ formData.sealReason }}
                                </div>

                                <div>部门领导会签</div>
                                <div
                                    style="
                                        grid-column: span 3;
                                        min-height: 64px;
                                    "
                                >
                                    <div
                                        v-for="item in formData.departmentHeader"
                                        :key="item.index"
                                    >
                                        <span
                                            >{{ item.name }}：{{
                                                item.content
                                            }}</span
                                        >
                                    </div>
                                </div>

                                <div>公司领导审批</div>
                                <div
                                    style="
                                        grid-column: span 3;
                                        min-height: 64px;
                                    "
                                >
                                    <div
                                        v-for="item in formData.companyHeader"
                                        :key="item.index"
                                    >
                                        <span
                                            >{{ item.name }}：{{
                                                item.content
                                            }}</span
                                        >
                                    </div>
                                </div>

                                <div>用章时间</div>
                                <div>{{ formData.sealDate }}</div>
                                <div>用章人签名</div>
                                <div>{{ formData.sealUser }}</div>
                            </div>

                            <div style="text-align: left">
                                填表说明：此表由用章人填好后交综合部保存
                            </div>
                        </form>
                    </v-card-text>
                </v-card>
            </v-col>

            <v-col cols="12" lg="4" xs="12">
                <v-card class="pa-4">
                    <v-tabs>
                        <v-tab @click="isShowTimeLine = false">内容</v-tab>
                        <v-tab @click="isShowTimeLine = true">时间线</v-tab>
                    </v-tabs>
                    <v-card-text v-show="!isShowTimeLine">
                        <v-form v-if="jobInfo.isCreating">
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.sealDepartment"
                                        :rules="[rules.required]"
                                        dense
                                        label="用章部门"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.sealName"
                                        :rules="[rules.required]"
                                        dense
                                        label="印章名称"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.sealTimes"
                                        :rules="[rules.required]"
                                        dense
                                        label="用章次数"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.sealReason"
                                        :rules="[rules.required]"
                                        dense
                                        label="用章事由"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.sealDate"
                                        :rules="[rules.required]"
                                        dense
                                        label="用章时间"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.sealUser"
                                        :rules="[rules.required]"
                                        dense
                                        label="用章人签名"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                        </v-form>
                        <form
                            v-if="
                                isEditable &&
                                (jobInfo.isReviewing || jobInfo.isSigning) &&
                                !jobInfo.isEnding
                            "
                        >
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="currentComment"
                                        dense
                                        label="请输入批示"
                                    ></v-text-field>
                                </v-col>
                            </v-row>
                            <ReviewChips :getChipValue="getChipValue" />
                        </form>
                        <v-row>
                            <v-col>
                                正文：
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.mainFiles"
                                    :getFileInfo="getMainFileInfo"
                                />
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                附件：
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.attachFiles"
                                    :getFileInfo="getAttachFileInfo"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>

                    <ActionButtons
                        v-if="isShowFunctionArea"
                        :currentComment="currentComment"
                        :currentCommentField="currentCommentField"
                        :formInfo="formInfo"
                        :isEditable="isEditable"
                        :isShowTimeLine="isShowTimeLine"
                        :jobInfo="jobInfo"
                        :nodeInfo="nodeInfo"
                        :workflowId="workflowId"
                    />
                    <v-card-text v-if="isShowTimeLine">
                        <TimelineCard
                            :jobList="jobInfo.jobList"
                            :nodeList="nodeInfo.nodeList"
                        />
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import print from 'vue-print-nb';
import FileUploadV1 from '../../components/FileUploadV1.vue';
import TimelineCard from '../../components/formFlow/TimelineCard.vue';
import { localUserInfo } from '../../helper/localUserInfo';

import initForm from '../../helper/initForm';
import isEditable from '../../helper/isEditable';

import ReviewChips from '../../components/formFlow/formComponent/ReviewChips.vue';
import ActionButtons from '../../components/formFlow/ActionButtons.vue';

export default {
    name: 'flow_1006',
    components: {
        FileUploadV1,
        TimelineCard,
        ReviewChips,
        ActionButtons,
    },
    directives: { print },
    data() {
        return {
            isShowFunctionArea: false,
            isEditable: false,
            isShowTimeLine: false,

            jobId: '',
            nodeId: '',
            workflowId: '',

            formInfo: {},
            jobInfo: {},
            nodeInfo: {},

            currentComment: '',
            currentCommentField: '',

            formData: {
                title: '',

                sealDepartment: '',
                sealName: '',
                sealTimes: '',
                sealReason: '',
                sealDate: '',
                sealUser: '',

                departmentHeader: [],
                companyHeader: [],

                mainFiles: '',
                attachFiles: '',
            },

            userId: localUserInfo.id,
            userName: localUserInfo.fullName,

            rules: {
                required: (value) => !!value || '此条目不能为空',
            },
        };
    },
    methods: {
        getMainFileInfo(filesList) {
            this.formData.mainFiles = filesList;
        },

        getAttachFileInfo(filesList) {
            this.formData.attachFiles = filesList;
        },

        getChipValue(chip) {
            this.currentComment = chip;
        },

        async initHtmlForm() {
            if (this.$route.query.workflowId) {
                this.workflowId = this.$route.query.workflowId;
                this.nodeId = this.$route.query.nodeId;
                this.jobId = this.$route.query.jobId;

                const initInfo = await initForm(
                    this.$axios,
                    this.jobId,
                    this.nodeId,
                    this.workflowId
                );
                this.formInfo = initInfo.formInfo;
                this.jobInfo = initInfo.jobInfo;
                this.nodeInfo = initInfo.nodeInfo;
                this.formData = initInfo.formInfo.formData;

                this.isEditable = isEditable(this.jobInfo.jobList, this.userId);
                this.isShowFunctionArea = true;

                if (!this.isEditable) return;
                if (this.jobInfo.isEnding) return;
                if (this.jobInfo.isCreating) return;

                this.currentCommentField =
                    this.nodeInfo.nodeTitle === '总经理'
                        ? 'companyHeader'
                        : 'departmentHeader';

                if (!this.jobInfo.isStaging) {
                    this.formData[this.currentCommentField].unshift({
                        name: this.userName,
                        content: '',
                    });
                }
                return;
            }

            this.formInfo.formData = this.formData;
            this.jobInfo.isCreating = true;
            this.isEditable = true;
            this.isShowFunctionArea = true;
        },
    },
    mounted() {
        this.initHtmlForm();
    },

    watch: {
        'formData.sealReason': function (newValue) {
            this.formData.title = newValue;
        },

        currentComment(newValue) {
            this.formData[this.currentCommentField][0].content = newValue;
        },
    },
};
</script>

<style scoped>
div {
    background-color: white;
}

input {
    resize: none;
    text-align: center;
    width: 100%;
    pointer-events: none;
}

.block1 {
    flex: 1;
    align-self: center;
}

.block3 {
    flex: 3;
    border-left: 1px solid black;
    align-self: center;
}

.block4 {
    border: black 1px solid;
    border-bottom: 0;
    display: flex;
}
</style>
