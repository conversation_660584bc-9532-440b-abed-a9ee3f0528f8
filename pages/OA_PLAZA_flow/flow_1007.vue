<template>
    <v-container fluid>
        <v-row>
            <v-col
                clos="12"
                lg="8"
                style="margin: 0 auto; max-width: 960px"
                xs="12"
            >
                <v-card class="pa-4">
                    <v-card-title>
                        <v-btn
                            color="primary"
                            icon
                            small
                            @click="$router.go(-1)"
                        >
                            <v-icon>mdi-arrow-left</v-icon>
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                            v-print="'#printForm'"
                            color="primary"
                            icon
                            small
                        >
                            <v-icon>mdi-printer</v-icon>
                        </v-btn>
                    </v-card-title>
                    <v-card-text>
                        <form
                            id="printForm"
                            style="color: black; line-height: 32px"
                        >
                            <h1 style="text-align: center">
                                北京新保利大厦房地产开发有限公司
                            </h1>
                            <h2 style="text-align: center">
                                经营类合同签订申请书
                            </h2>

                            <div style="text-align: right">
                                申请日期:
                                <span>formData.date</span>
                            </div>

                            <div
                                style="
                                    display: grid;
                                    grid-template-columns: 1fr;
                                    background-color: black;
                                    gap: 1px;
                                    border: solid 1px black;
                                "
                            >
                                <div>合同编号：{{ formData.number }}</div>

                                <div>合同名称：{{ formData.title }}</div>

                                <div>合同甲方：{{ formData.contractA }}</div>

                                <div>合同乙方：{{ formData.contractB }}</div>

                                <div>
                                    合同总额：{{ formData.contractAmount }}
                                </div>

                                <div>
                                    内容摘要：{{ formData.contractContents }}
                                </div>

                                <div>
                                    付款方式：{{ formData.paymentMethod }}
                                </div>

                                <div>备注：{{ formData.note }}</div>

                                <div
                                    style="
                                        display: grid;
                                        grid-template-columns: repeat(5, 1fr);
                                        gap: 1px;
                                        background-color: black;
                                        min-height: 64px;
                                    "
                                >
                                    <div>
                                        承办部门意见：
                                        <div
                                            v-for="item in formData.undertakingDep"
                                            :key="item.index"
                                        >
                                            <span>{{ item.content }}</span>
                                        </div>
                                    </div>
                                    <div>
                                        财务部门意见：
                                        <div
                                            v-for="item in formData.financeDepartment"
                                            :key="item.index"
                                        >
                                            <span
                                                >{{ item.name }}
                                                {{ item.content }}</span
                                            >
                                        </div>
                                    </div>
                                    <div>
                                        主管领导意见：
                                        <div
                                            v-for="item in formData.supervisor"
                                            :key="item.index"
                                        >
                                            <span>{{ item.content }}</span>
                                        </div>
                                    </div>
                                    <div>
                                        总会计师意见：
                                        <div
                                            v-for="item in formData.chiefAccountant"
                                            :key="item.index"
                                        >
                                            <span>{{ item.content }}</span>
                                        </div>
                                    </div>
                                    <div>
                                        总经理意见：
                                        <div
                                            v-for="item in formData.generalManager"
                                            :key="item.index"
                                        >
                                            <span>{{ item.content }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div style="text-align: right">
                                合同承办人：
                                <span>{{ formData.contractor }}</span>
                            </div>

                            <div>
                                本表一式两份，合同承办部门、财务部各一份。
                            </div>
                        </form>
                    </v-card-text>
                </v-card>
            </v-col>

            <v-col cols="12" lg="4" xs="12">
                <v-card class="pa-4">
                    <v-tabs>
                        <v-tab @click="isShowTimeLine = false">内容</v-tab>
                        <v-tab @click="isShowTimeLine = true">时间线</v-tab>
                    </v-tabs>
                    <v-card-text v-show="!isShowTimeLine">
                        <form v-if="jobInfo.isCreating">
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.date"
                                        :rules="[rules.required]"
                                        dense
                                        label="申请日期"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.number"
                                        :rules="[rules.required]"
                                        dense
                                        label="合同编号"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.title"
                                        :rules="[rules.required]"
                                        dense
                                        label="合同名称"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.contractA"
                                        :rules="[rules.required]"
                                        dense
                                        label="合同甲方"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.contractB"
                                        :rules="[rules.required]"
                                        dense
                                        label="合同乙方"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.contractAmount"
                                        :rules="[rules.required]"
                                        dense
                                        label="合同总额"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.contractContents"
                                        :rules="[rules.required]"
                                        dense
                                        label="内容摘要"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.paymentMethod"
                                        :rules="[rules.required]"
                                        dense
                                        label="付款方式"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.note"
                                        dense
                                        label="备注"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.contractor"
                                        :rules="[rules.required]"
                                        dense
                                        label="合同承办人"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                        </form>
                        <form
                            v-if="
                                isEditable &&
                                (jobInfo.isReviewing || jobInfo.isSigning) &&
                                !jobInfo.isEnding
                            "
                        >
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="currentComment"
                                        dense
                                        label="请输入批示"
                                    ></v-text-field>
                                </v-col>
                            </v-row>
                            <ReviewChips :getChipValue="getChipValue" />
                        </form>
                        <v-row>
                            <v-col>
                                正文：
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.mainFiles"
                                    :getFileInfo="getMainFileInfo"
                                />
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                附件：
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.attachFiles"
                                    :getFileInfo="getAttachFileInfo"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>

                    <ActionButtons
                        v-if="isShowFunctionArea"
                        :currentComment="currentComment"
                        :currentCommentField="currentCommentField"
                        :formInfo="formInfo"
                        :isEditable="isEditable"
                        :isShowTimeLine="isShowTimeLine"
                        :jobInfo="jobInfo"
                        :nodeInfo="nodeInfo"
                        :workflowId="workflowId"
                    />
                    <v-card-text v-if="isShowTimeLine">
                        <TimelineCard
                            :jobList="jobInfo.jobList"
                            :nodeList="nodeInfo.nodeList"
                        />
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>
<script>
import print from 'vue-print-nb';
import FileUploadV1 from '../../components/FileUploadV1.vue';
import TimelineCard from '../../components/formFlow/TimelineCard.vue';
import { localUserInfo } from '../../helper/localUserInfo';

import initForm from '../../helper/initForm';
import isEditable from '../../helper/isEditable';

import ReviewChips from '../../components/formFlow/formComponent/ReviewChips.vue';
import ActionButtons from '../../components/formFlow/ActionButtons.vue';

export default {
    name: 'flow_1007',
    components: {
        FileUploadV1,
        TimelineCard,
        ReviewChips,
        ActionButtons,
    },
    directives: { print },
    data() {
        return {
            isShowFunctionArea: false,
            isEditable: false,
            isShowTimeLine: false,

            jobId: '',
            nodeId: '',
            workflowId: '',

            formInfo: {},
            jobInfo: {},
            nodeInfo: {},

            currentComment: '',
            currentCommentField: '',

            formData: {
                date: '',
                number: '',
                title: '',
                contractA: '',
                contractB: '',
                contractAmount: '',
                contractContents: '',
                paymentMethod: '',
                note: '',
                contractor: '',

                financeDepartment: [],
                undertakingDep: [],
                supervisor: [],
                chiefAccountant: [],
                generalManager: [],

                mainFiles: '',
                attachFiles: '',
            },

            userId: localUserInfo.id,
            userName: localUserInfo.fullName,

            rules: {
                required: (value) => !!value || '此条目不能为空',
            },
        };
    },

    methods: {
        getMainFileInfo(filesList) {
            this.formData.mainFiles = filesList;
        },

        getAttachFileInfo(filesList) {
            this.formData.attachFiles = filesList;
        },

        getChipValue(chip) {
            this.currentComment = chip;
        },

        async initHtmlForm() {
            if (this.$route.query.workflowId) {
                this.workflowId = this.$route.query.workflowId;
                this.nodeId = this.$route.query.nodeId;
                this.jobId = this.$route.query.jobId;

                const initInfo = await initForm(
                    this.$axios,
                    this.jobId,
                    this.nodeId,
                    this.workflowId
                );
                this.formInfo = initInfo.formInfo;
                this.jobInfo = initInfo.jobInfo;
                this.nodeInfo = initInfo.nodeInfo;
                this.formData = initInfo.formInfo.formData;

                this.isEditable = isEditable(this.jobInfo.jobList, this.userId);
                this.isShowFunctionArea = true;

                if (!this.isEditable) return;
                if (this.jobInfo.isEnding) return;
                if (this.jobInfo.isCreating) return;

                switch (this.nodeInfo.nodeTitle) {
                    case '承办部门负责人':
                        this.currentCommentField = 'undertakingDep';
                        break;
                    case '财务部审批':
                        this.currentCommentField = 'financeDepartment';
                        break;
                    case '主管领导':
                        this.currentCommentField = 'supervisor';
                        break;
                    case '总会计师':
                        this.currentCommentField = 'chiefAccountant';
                        break;
                    case '总经理':
                        this.currentCommentField = 'generalManager';
                        break;
                    default:
                        break;
                }

                if (!this.jobInfo.isStaging) {
                    this.formData[this.currentCommentField].unshift({
                        name: this.userName,
                        content: '',
                    });
                }

                return;
            }

            this.formInfo.formData = this.formData;
            this.jobInfo.isCreating = true;
            this.isEditable = true;
            this.isShowFunctionArea = true;
        },
    },

    mounted() {
        this.initHtmlForm();
    },

    watch: {
        currentComment(newValue) {
            this.formData[this.currentCommentField][0].content = newValue;
        },
    },
};
</script>
<style scoped>
div {
    background-color: white;
}
</style>
