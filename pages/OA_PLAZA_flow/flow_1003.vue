<template>
    <v-container fluid>
        <v-row>
            <v-col
                clos="12"
                lg="8"
                style="margin: 0 auto; max-width: 960px"
                xs="12"
            >
                <v-card class="pa-4">
                    <v-card-title>
                        <v-btn
                            color="primary"
                            icon
                            small
                            @click="$router.go(-1)"
                        >
                            <v-icon>mdi-arrow-left</v-icon>
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                            v-print="'#printForm'"
                            color="primary"
                            icon
                            small
                        >
                            <v-icon>mdi-printer</v-icon>
                        </v-btn>
                    </v-card-title>
                    <v-card-text>
                        <form
                            id="printForm"
                            style="
                                color: black;
                                line-height: 32px;
                                text-align: center;
                            "
                        >
                            <h1>北京新保利大厦房地产开发有限公司</h1>
                            <h2>收文传阅单</h2>

                            <div
                                class="grid1_3"
                                style="border: solid 1px black"
                            >
                                <div>来文单位</div>
                                <div>{{ formData.communicationUnit }}</div>
                                <div>文件号</div>
                                <div>{{ formData.fileNumber }}</div>

                                <div>文件标题</div>
                                <div style="min-height: 0; grid-column: span 3">
                                    {{ formData.title }}
                                </div>

                                <div style="grid-column: span 4">领导批示</div>

                                <div
                                    v-for="user in reviewUserList"
                                    :key="user.uid"
                                    class="grid1_3"
                                >
                                    <div>
                                        {{ user.name }}
                                    </div>
                                    <div style="grid-column: span 3">
                                        <div
                                            v-for="comment in formData[
                                                user.pinyin
                                            ]"
                                            :key="comment.index"
                                        >
                                            <span>{{ comment.content }}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="grid1_3">
                                    <div style="grid-column: span 1">
                                        综合部门意见
                                    </div>
                                    <div style="grid-column: span 3">
                                        <div
                                            v-for="item in formData.generalDepartment"
                                            :key="item.index"
                                        >
                                            <span
                                                >{{ item.name }}：{{
                                                    item.content
                                                }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </v-card-text>
                </v-card>
            </v-col>

            <v-col cols="12" lg="4" xs="12">
                <v-card class="pa-4">
                    <v-tabs>
                        <v-tab @click="isShowTimeLine = false">内容</v-tab>
                        <v-tab @click="isShowTimeLine = true">时间线</v-tab>
                    </v-tabs>
                    <v-card-text v-show="!isShowTimeLine">
                        <v-form v-if="jobInfo.isCreating">
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.communicationUnit"
                                        :rules="[rules.required]"
                                        dense
                                        label="来文单位"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.fileNumber"
                                        :rules="[rules.required]"
                                        dense
                                        label="文件号"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.title"
                                        :rules="[rules.required]"
                                        dense
                                        label="文件标题"
                                    >
                                    </v-text-field>
                                </v-col>
                            </v-row>
                        </v-form>
                        <v-form
                            v-if="
                                isEditable &&
                                (jobInfo.isReviewing || jobInfo.isSigning) &&
                                !jobInfo.isEnding
                            "
                        >
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="currentComment"
                                        dense
                                        label="请输入批示"
                                    ></v-text-field>
                                </v-col>
                            </v-row>
                            <ReviewChips :getChipValue="getChipValue" />
                        </v-form>
                        <v-row>
                            <v-col>
                                正文：
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.mainFiles"
                                    :getFileInfo="getMainFileInfo"
                                />
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                附件：
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.attachFiles"
                                    :getFileInfo="getAttachFileInfo"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>
                    <ActionButtons
                        v-if="isShowFunctionArea"
                        :currentComment="currentComment"
                        :currentCommentField="currentCommentField"
                        :formInfo="formInfo"
                        :isEditable="isEditable"
                        :isShowTimeLine="isShowTimeLine"
                        :jobInfo="jobInfo"
                        :nodeInfo="nodeInfo"
                        :workflowId="workflowId"
                    />
                    <v-card-text v-if="isShowTimeLine">
                        <TimelineCard
                            :jobList="jobInfo.jobList"
                            :nodeList="nodeInfo.nodeList"
                        />
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>
<script>
import print from 'vue-print-nb';
import FileUploadV1 from '../../components/FileUploadV1.vue';
import TimelineCard from '../../components/formFlow/TimelineCard.vue';
import { localUserInfo } from '../../helper/localUserInfo';

import initForm from '../../helper/initForm';
import isEditable from '../../helper/isEditable';

import ReviewChips from '../../components/formFlow/formComponent/ReviewChips.vue';
import ActionButtons from '../../components/formFlow/ActionButtons.vue';

export default {
    name: 'flow_1003',
    components: {
        FileUploadV1,
        TimelineCard,
        ReviewChips,
        ActionButtons,
    },
    directives: { print },
    data() {
        return {
            isShowFunctionArea: false,
            isEditable: false,
            isShowTimeLine: false,

            jobId: '',
            nodeId: '',
            workflowId: '',

            formInfo: {},
            jobInfo: {},
            nodeInfo: {},

            currentComment: '',
            currentCommentField: '',

            formData: {
                communicationUnit: '',
                fileNumber: '',
                title: '',

                WB: [],
                SZG: [],
                YD: [],
                SP: [],
                SY: [],
                CHT: [],
                XY: [],
                generalDepartment: [],

                mainFiles: '',
                attachFiles: '',

                reviewUserList: [],
            },

            reviewUserList: [
                {
                    uid: '1ccf2cd0-757e-11ea-b2e6-e704e56e5f96',
                    pinyin: 'WB',
                    name: '卫飚',
                },
                {
                    uid: 'dc0dc6f0-6a80-11ea-a11d-27a6607da11a',
                    pinyin: 'SZG',
                    name: '石志刚',
                },
                {
                    uid: 'dce33f60-6a80-11ea-a11d-27a6607da11a',
                    pinyin: 'YD',
                    name: '杨栋',
                },
                {
                    uid: '06ce4c30-55dd-11ed-be88-5b9dff5bf1a0',
                    pinyin: 'SP',
                    name: '史鹏',
                },
                {
                    uid: 'ac9576f0-51cf-11ed-9d21-8794174e5a93',
                    pinyin: 'SY',
                    name: '沈燕',
                },
                {
                    uid: 'dc878ad1-6a80-11ea-a11d-27a6607da11a',
                    pinyin: 'CHT',
                    name: '陈洪涛',
                },
                {
                    uid: '06e110e0-55dd-11ed-be88-5b9dff5bf1a0',
                    pinyin: 'XY',
                    name: '许毅',
                },
            ],

            userId: localUserInfo.id,
            userName: localUserInfo.fullName,

            rules: {
                required: (value) => !!value || '此条目不能为空',
            },
        };
    },

    methods: {
        getMainFileInfo(filesList) {
            this.formData.mainFiles = filesList;
        },

        getAttachFileInfo(filesList) {
            this.formData.attachFiles = filesList;
        },

        getChipValue(chip) {
            this.currentComment = chip;
        },

        async initHtmlForm() {
            if (this.$route.query.workflowId) {
                this.workflowId = this.$route.query.workflowId;
                this.nodeId = this.$route.query.nodeId;
                this.jobId = this.$route.query.jobId;

                const initInfo = await initForm(
                    this.$axios,
                    this.jobId,
                    this.nodeId,
                    this.workflowId
                );
                this.formInfo = initInfo.formInfo;
                this.jobInfo = initInfo.jobInfo;
                this.nodeInfo = initInfo.nodeInfo;
                this.formData = initInfo.formInfo.formData;
                this.reviewUserList = this.formData.reviewUserList;

                this.isEditable = isEditable(this.jobInfo.jobList, this.userId);
                this.isShowFunctionArea = true;

                if (!this.isEditable) return;
                if (this.jobInfo.isEnding) return;
                if (this.jobInfo.isCreating) return;

                const userOnForm = this.formData.reviewUserList.find(
                    (item) => item.uid === this.userId
                );

                this.currentCommentField = userOnForm
                    ? userOnForm.pinyin
                    : 'generalDepartment';

                if (!this.jobInfo.isStaging) {
                    this.formData[this.currentCommentField].unshift({
                        name: this.userName,
                        content: '',
                    });
                }

                return;
            }
            this.formData.reviewUserList = this.reviewUserList;
            this.formInfo.formData = this.formData;
            this.jobInfo.isCreating = true;
            this.isEditable = true;
            this.isShowFunctionArea = true;
        },
    },

    mounted() {
        this.initHtmlForm();
    },

    watch: {
        currentComment(newValue) {
            this.formData[this.currentCommentField][0].content = newValue;
        },
    },
};
</script>
<style scoped>
div {
    background-color: white;
}

span {
    color: black;
}

.grid1_3 {
    grid-column: span 4;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1px;
    background-color: black;
    min-height: 64px;
}
</style>
