<template>
    <v-container fluid>
        <v-row>
            <v-col
                clos="12"
                lg="8"
                style="margin: 0 auto; max-width: 960px"
                xs="12"
            >
                <v-card class="pa-4">
                    <v-card-title>
                        <v-btn
                            color="primary"
                            icon
                            small
                            @click="$router.go(-1)"
                        >
                            <v-icon>mdi-arrow-left</v-icon>
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                            v-print="'#printForm'"
                            color="primary"
                            icon
                            small
                        >
                            <v-icon>mdi-printer</v-icon>
                        </v-btn>
                    </v-card-title>
                    <v-card-text>
                        <form
                            id="printForm"
                            style="
                                color: red;
                                line-height: 24px;
                                display: grid;
                                grid-template-columns: 1fr;
                                gap: 1px;
                                background: red;
                            "
                        >
                            <div>
                                <h1
                                    style="
                                        text-align: center;
                                        margin-bottom: 24px;
                                    "
                                >
                                    呈&nbsp;&nbsp;&nbsp;&nbsp;批&nbsp;&nbsp;&nbsp;&nbsp;件
                                </h1>

                                <div>
                                    北京新保利大厦房地产开发有限公司
                                    <div
                                        v-if="formInfo.docNumber"
                                        style="float: right"
                                    >
                                        新保办文第<span>{{
                                            formInfo.docNumber
                                        }}</span
                                        >号
                                    </div>
                                </div>
                            </div>

                            <div>
                                题目：
                                <span>{{ formData.title }}</span>
                            </div>

                            <div
                                v-for="user in reviewUserList"
                                :key="user.uid"
                                style="min-height: 96px"
                            >
                                <div>{{ user.name }}批示：</div>
                                <div
                                    v-for="comment in formData[user.pinyin]"
                                    :key="comment.index"
                                >
                                    <span>{{ comment.content }}</span>
                                </div>
                            </div>

                            <div style="min-height: 96px">
                                <div>相关部门意见：</div>
                                <div
                                    v-for="item in formData.mainOrganizers"
                                    :key="item.index"
                                >
                                    <span
                                        >{{ item.name }}：{{
                                            item.content
                                        }}</span
                                    >
                                </div>
                            </div>

                            <div style="display: flex">
                                <div style="flex: 1">
                                    承办人：
                                    <span>{{ formData.promoter }}</span>
                                </div>
                                <div style="flex: 1">
                                    电话：
                                    <span>{{ formData.tel }}</span>
                                </div>
                            </div>
                        </form>
                    </v-card-text>
                </v-card>
            </v-col>

            <v-col cols="12" lg="4" xs="12">
                <v-card class="pa-4">
                    <v-tabs>
                        <v-tab @click="isShowTimeLine = false">内容</v-tab>
                        <v-tab @click="isShowTimeLine = true">时间线</v-tab>
                    </v-tabs>
                    <v-card-text v-show="!isShowTimeLine">
                        <v-form v-if="jobInfo.isCreating">
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="formData.title"
                                        :rules="[rules.required]"
                                        dense
                                        label="题目"
                                    ></v-text-field>
                                </v-col>
                            </v-row>
                        </v-form>
                        <v-form
                            v-if="
                                isEditable &&
                                (jobInfo.isReviewing || jobInfo.isSigning) &&
                                !jobInfo.isEnding
                            "
                        >
                            <v-row>
                                <v-col>
                                    <v-text-field
                                        v-model="currentComment"
                                        dense
                                        label="请输入批示"
                                    ></v-text-field>
                                </v-col>
                            </v-row>
                            <ReviewChips :getChipValue="getChipValue" />
                        </v-form>
                        <v-row>
                            <v-col>
                                正文：
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.mainFiles"
                                    :getFileInfo="getMainFileInfo"
                                />
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                附件：
                                <FileUploadV1
                                    :allowUpload="
                                        jobInfo.isCreating
                                            ? true
                                            : nodeInfo.fileModify
                                    "
                                    :filesProps="formData.attachFiles"
                                    :getFileInfo="getAttachFileInfo"
                                />
                            </v-col>
                        </v-row>
                    </v-card-text>
                    <ActionButtons
                        v-if="isShowFunctionArea"
                        :currentComment="currentComment"
                        :currentCommentField="currentCommentField"
                        :formInfo="formInfo"
                        :isEditable="isEditable"
                        :isShowTimeLine="isShowTimeLine"
                        :jobInfo="jobInfo"
                        :nodeInfo="nodeInfo"
                        :workflowId="workflowId"
                    />

                    <v-card-text v-if="isShowTimeLine">
                        <TimelineCard
                            :jobList="jobInfo.jobList"
                            :nodeList="nodeInfo.nodeList"
                        />
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>
<script>
import print from 'vue-print-nb';
import FileUploadV1 from '../../components/FileUploadV1.vue';
import TimelineCard from '../../components/formFlow/TimelineCard.vue';
import { localUserInfo } from '../../helper/localUserInfo';

import initForm from '../../helper/initForm';
import isEditable from '../../helper/isEditable';

import ReviewChips from '../../components/formFlow/formComponent/ReviewChips.vue';
import ActionButtons from '../../components/formFlow/ActionButtons.vue';

export default {
    name: 'flow_1001',
    components: {
        FileUploadV1,
        TimelineCard,
        ReviewChips,
        ActionButtons,
    },
    directives: { print },
    data() {
        return {
            isShowFunctionArea: false,
            isEditable: false,
            isShowTimeLine: false,

            jobId: '',
            nodeId: '',
            workflowId: '',

            formInfo: {},
            jobInfo: {},
            nodeInfo: {},

            currentComment: '',
            currentCommentField: '',

            formData: {
                title: '',
                promoter: '',
                tel: '',

                WB: [],
                SZG: [],
                YD: [],
                mainOrganizers: [],

                mainFiles: '',
                attachFiles: '',

                reviewUserList: [],
            },

            reviewUserList: [
                {
                    uid: '1ccf2cd0-757e-11ea-b2e6-e704e56e5f96',
                    pinyin: 'WB',
                    name: '卫飚总经理',
                },
                {
                    uid: 'dc0dc6f0-6a80-11ea-a11d-27a6607da11a',
                    pinyin: 'SZG',
                    name: '石志刚副总经理',
                },
                {
                    uid: 'dce33f60-6a80-11ea-a11d-27a6607da11a',
                    pinyin: 'YD',
                    name: '杨栋总会计师',
                },
            ],

            userId: localUserInfo.id,
            userName: localUserInfo.fullName,

            rules: {
                required: (value) => !!value || '此条目不能为空',
            },
        };
    },

    methods: {
        getMainFileInfo(filesList) {
            this.formData.mainFiles = filesList;
        },

        getAttachFileInfo(filesList) {
            this.formData.attachFiles = filesList;
        },

        getChipValue(chip) {
            this.currentComment = chip;
        },

        async initHtmlForm() {
            if (this.$route.query.workflowId) {
                this.workflowId = this.$route.query.workflowId;
                this.nodeId = this.$route.query.nodeId;
                this.jobId = this.$route.query.jobId;

                const initInfo = await initForm(
                    this.$axios,
                    this.jobId,
                    this.nodeId,
                    this.workflowId
                );
                this.formInfo = initInfo.formInfo;
                this.jobInfo = initInfo.jobInfo;
                this.nodeInfo = initInfo.nodeInfo;
                this.formData = initInfo.formInfo.formData;
                this.reviewUserList = this.formData.reviewUserList;

                this.isEditable = isEditable(this.jobInfo.jobList, this.userId);
                this.isShowFunctionArea = true;

                if (!this.isEditable) return;
                if (this.jobInfo.isEnding) return;
                if (this.jobInfo.isCreating) return;

                const userOnForm = this.formData.reviewUserList.find(
                    (item) => item.uid === this.userId
                );

                this.currentCommentField = userOnForm
                    ? userOnForm.pinyin
                    : 'mainOrganizers';

                if (!this.jobInfo.isStaging) {
                    this.formData[this.currentCommentField].unshift({
                        name: this.userName,
                        content: '',
                    });
                }

                return;
            }

            this.formData.promoter = this.userName;
            this.formData.tel = localUserInfo.mobile;
            this.formData.reviewUserList = this.reviewUserList;
            this.formInfo.formData = this.formData;
            this.jobInfo.isCreating = true;
            this.isEditable = true;
            this.isShowFunctionArea = true;
        },
    },

    mounted() {
        this.initHtmlForm();
    },

    watch: {
        currentComment(newValue) {
            this.formData[this.currentCommentField][0].content = newValue;
        },
    },
};
</script>

<style scoped>
div {
    background-color: white;
}

span {
    color: black;
}
</style>
