<template>
    <div class="user-center-redirect">
        <div class="login_bg" />
        <div class="login_content">
            <div class="content">
                <img
                    :src="appLogo"
                    class="img-responsive mb-4"
                    height="70"
                    width="70"
                />
                <div class="desc">登录</div>
                <v-btn
                    class="redirect-btn"
                    color="primary"
                    elevation="2"
                    large
                    @click="handleRedirectLogin"
                >
                    统一身份认证登录
                </v-btn>
            </div>
        </div>
    </div>
</template>

<script>
import { getCustomOAuthRedirectUri } from '../constants/poly-auth';
import AppConfig from '../constants/AppConfig';

export default {
    name: 'loginRedirect',
    data() {
        return {
            appLogo: AppConfig.appLogo2,
        };
    },
    methods: {
        handleRedirectLogin() {
            window.location.href = getCustomOAuthRedirectUri();
        },
    },
};
</script>

<style scoped>
.user-center-redirect {
    height: 100%;
    display: flex;
    overflow: hidden;
    background-color: #f2f4f8;
}

.login_bg {
    width: 437px;
    height: 100%;
    background-image: url('/login/login2022.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.login_content {
    flex: 1;
}

.content {
    margin: 15rem auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.desc {
    font-size: 20px;
    font-weight: 600;
    letter-spacing: 0.2rem;
    margin: 0 0 20px 0;
}

.redirect-btn {
    width: 20rem;
    letter-spacing: 0.12rem;
}

@media screen and (max-width: 480px) {
    .login_bg {
        display: none;
    }
}
</style>
