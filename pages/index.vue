<template>
    <v-container fluid>
        <v-row>
            <v-col cols="12" lg="5" xs="12">
                <WaitingHandle />
            </v-col>
            <v-col cols="12" lg="4" xs="12">
                <WaitingRead />
            </v-col>
            <v-col cols="12" lg="3" xs="12">
                <NoticeAnnouncement />
            </v-col>
        </v-row>
        <v-row>
            <v-col cols="12" lg="9" xs="12">
                <InformationList />
            </v-col>
            <v-col cols="12" lg="3" xs="12">
                <LinkCard />
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import WaitingHandle from '../components/index/WaitingHandle.vue';
import WaitingRead from '../components/index/WaitingRead.vue';
import NoticeAnnouncement from '../components/index/NoticeAnnouncement.vue';
import InformationList from '../components/index/InformationList.vue';
import LinkCard from '../components/index/LinkCard.vue';

export default {
    name: 'IndexPage',
    components: {
        WaitingHandle,
        WaitingRead,
        NoticeAnnouncement,
        InformationList,
        LinkCard,
    },
};
</script>
