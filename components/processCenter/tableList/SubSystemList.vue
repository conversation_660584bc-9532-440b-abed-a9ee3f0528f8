<template>
    <v-card-text>
        <v-spacer>
            <v-chip-group active-class="primary--text" column mandatory>
                <v-chip
                    v-for="tag in tagList"
                    :key="tag.type"
                    @click="changeTag(tag.type)"
                    >{{ tag.label }}
                </v-chip>
            </v-chip-group>
            <div style="display: flex; align-items: center">
                <v-text-field
                    v-model="searchContent"
                    clearable
                    label="标题"
                ></v-text-field>
                <v-btn class="ml-2" color="primary" @click="searchContentList"
                    >查找
                </v-btn>
            </div>
        </v-spacer>

        <v-simple-table>
            <thead>
                <tr>
                    <th>标题</th>
                    <th>系统名称</th>
                    <th>创建时间</th>
                </tr>
            </thead>
            <tbody style="cursor: pointer">
                <tr v-for="item in subSystemList" :key="item.id">
                    <td>{{ item.title }}</td>
                    <td>
                        <v-chip
                            :color="sys_code[item.from_system_code].color"
                            small
                        >
                            {{ sys_code[item.from_system_code].name }}
                        </v-chip>
                    </td>
                    <td>{{ item.created_at }}</td>
                </tr>
            </tbody>
        </v-simple-table>

        <v-divider></v-divider>
        <v-pagination
            v-model="currentPage"
            :length="Math.ceil(this.listCount / 50)"
            :total-visible="5"
        ></v-pagination>
    </v-card-text>
</template>

<script>
export default {
    name: 'SubSystemList',
    data() {
        return {
            tagList: [
                { type: '-1', label: '全部' },
                { type: '1', label: '呈批件' },
                { type: '2', label: '阅件' },
                { type: '!1,2', label: '其他' },
            ],

            currentType: '-1',

            searchContent: '',

            subSystemList: [],

            sys_code: {
                OA_FINANCE: {
                    color: 'amber accent-2',
                    name: '财务',
                },
                MEETING_FILE: {
                    color: 'purple lighten-3',
                    name: '会议',
                },
                CHAILV: {
                    color: 'teal lighten-3',
                    name: '差旅',
                },
                BAOXIAO: {
                    color: 'purple lighten-3',
                    name: '报销',
                },
            },

            listCount: '',
            currentPage: 1,
        };
    },
    methods: {
        async changeTag(type) {
            this.currentType = type;
            this.currentPage = 1;

            if (type === '-1') {
                this.getSubSystemList();
                return;
            }

            const result = await this.$axios.get(
                `/api/be/oa/myjob/indexlist4hq?action_type=!3&business_type=3rd_system_todo&page=0&size=50&source_type=${type}`
            );

            if (result.state === 'error') {
                alert(result.msg);
                return;
            }

            this.subSystemList = result.rows;
            this.listCount = result.count;
        },

        async searchContentList() {
            this.currentPage = 1;
            if (!this.searchContent) {
                this.getSubSystemList();
                return;
            }

            const result = await this.$axios.get(
                `/api/be/oa/myjob/indexlist4hq?action_type=!3&business_type=3rd_system_todo&page=0&size=50&_name=${this.searchContent}`
            );

            if (result.state === 'error') {
                alert(result.msg);
                return;
            }

            this.searchContent = result.rows;
            this.listCount = result.count;
        },
        async getSubSystemList() {
            const result = await this.$axios.get(
                '/api/be/oa/myjob/indexlist4hq?action_type=!3&business_type=3rd_system_todo&page=0&size=50'
            );

            if (result.state === 'error') {
                alert(result.msg);
                return;
            }

            this.subSystemList = result.rows;
            this.listCount = result.count;
        },
    },
    mounted() {
        this.getSubSystemList();
    },
    watch: {
        async currentPage() {
            let result;
            if (this.currentType === '-1') {
                result = await this.$axios.get(
                    `/api/be/oa/myjob/indexlist4hq?action_type=!3&business_type=3rd_system_todo&page=${this.currentPage}&size=50`
                );
            } else {
                result = await this.$axios.get(
                    `/api/be/oa/myjob/indexlist4hq?action_type=!3&business_type=3rd_system_todo&page=${this.currentPage}&size=50&source_type=${this.currentType}`
                );
            }

            if (result.state === 'error') {
                alert(result.msg);
                return;
            }
            this.subSystemList = result.rows;
            this.listCount = result.count;
        },
    },
};
</script>

<style scoped></style>
