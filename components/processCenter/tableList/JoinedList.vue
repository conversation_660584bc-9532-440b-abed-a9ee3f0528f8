<template>
    <v-card-text>
        <v-spacer>
            <v-chip-group active-class="primary--text" column mandatory>
                <v-chip
                    v-for="tag in tagList"
                    :key="tag.type"
                    @click="changeTag(tag.type)"
                    >{{ tag.label }}
                </v-chip>
            </v-chip-group>
            <div style="display: flex; align-items: center">
                <v-text-field
                    v-model="searchContent"
                    clearable
                    label="标题"
                ></v-text-field>
                <v-btn class="ml-2" color="primary" @click="searchContentList"
                    >查找
                </v-btn>
            </div>
        </v-spacer>
        <div style="overflow: auto">
            <v-simple-table style="min-width: 300px">
                <thead>
                    <tr>
                        <th>标题</th>
                        <th>流程名称</th>
                        <th v-if="!$vuetify.breakpoint.xs">当前办理人</th>
                        <th v-if="!$vuetify.breakpoint.xs">起草时间</th>
                    </tr>
                </thead>
                <tbody style="cursor: pointer">
                    <tr
                        v-for="item in joinedList"
                        :key="item.id"
                        @click="handleClick(item)"
                    >
                        <td>{{ item.title }}</td>
                        <td>{{ item.Source?.name }}</td>
                        <td v-if="!$vuetify.breakpoint.xs">
                            {{
                                formatCurrentHandler(item.CurrentJob) ||
                                '已完成'
                            }}
                        </td>
                        <td v-if="!$vuetify.breakpoint.xs">
                            {{ item.created_at }}
                        </td>
                    </tr>
                </tbody>
            </v-simple-table>
        </div>

        <v-divider></v-divider>
        <v-pagination
            v-model="currentPage"
            :length="Math.ceil(this.listCount / 50)"
            :total-visible="5"
        ></v-pagination>
    </v-card-text>
</template>

<script>
import AppConfig from '../../../constants/AppConfig';
import currentPromoter from '../../../helper/currentPromoter';

export default {
    name: 'JoinedList',
    data() {
        return {
            tagList: [
                { type: '-1', label: '全部' },
                { type: '1', label: '呈批件' },
                { type: '2', label: '阅件' },
                { type: '!1,2', label: '其他' },
            ],

            currentType: '-1',

            searchContent: '',

            joinedList: [],

            listCount: '',
            currentPage: 1,
        };
    },
    methods: {
        async changeTag(type) {
            this.currentType = type;
            this.currentPage = 1;

            if (type === '-1') {
                await this.getJoinedList();
                return;
            }
            const result = await this.$axios.get(
                `/api/be/workflow/list/joined?type=1&page=0&size=50&source_type=${type}`
            );

            if (result.state === 'error') {
                alert(result.errorMsg);
                return;
            }

            this.joinedList = result.rows;
            this.listCount = result.count;
        },
        async searchContentList() {
            this.currentPage = 1;

            if (!this.searchContent) {
                await this.getJoinedList();
                return;
            }

            const result = await this.$axios.get(
                `/api/be/workflow/list/joined?type=1&page=0&size=50&_name=${this.searchContent}`
            );

            if (result.state === 'error') {
                alert(result.errorMsg);
                return;
            }

            this.joinedList = result.rows;
            this.listCount = result.count;
        },
        async getJoinedList() {
            const result = await this.$axios.get(
                '/api/be/workflow/list/joined?type=1&page=0&size=50'
            );

            if (result.state === 'error') {
                alert(result.errorMsg);
                return;
            }

            this.joinedList = result.rows;
            this.listCount = result.count;
        },
        formatCurrentHandler(jobs) {
            return currentPromoter(jobs);
        },
        handleClick(item) {
            this.$router.push({
                path: `/${AppConfig.sys_code_prefix}_flow/flow_${item.sourceid}`,
                query: { workflowId: item.id },
            });
        },
    },
    mounted() {
        this.getJoinedList();
    },
    watch: {
        async currentPage() {
            let result;
            if (this.currentType === '-1') {
                result = await this.$axios.get(
                    `/api/be/workflow/list/joined?type=1&page=${
                        this.currentPage - 1
                    }&size=50`
                );
            } else {
                result = await this.$axios.get(
                    `/api/be/workflow/list/joined?type=1&page=${
                        this.currentPage - 1
                    }&size=50&source_type=${this.currentType}`
                );
            }

            if (result.state === 'error') {
                alert(result.errorMsg);
                return;
            }
            this.joinedList = result.rows;
            this.listCount = result.count;
        },
    },
};
</script>

<style scoped></style>
