<template>
    <!--    <v-card style="position: fixed; max-width: 400px">-->

    <v-row>
        <v-col v-if="!$vuetify.breakpoint.xs" cols="12" lg="3">
            <v-card>
                <v-expansion-panels accordion>
                    <v-expansion-panel
                        v-for="item of sourceList"
                        :key="item.id"
                    >
                        <v-expansion-panel-header
                            >{{ item.name }}
                        </v-expansion-panel-header>
                        <v-expansion-panel-content>
                            <v-list>
                                <v-list-item-group>
                                    <v-list-item
                                        v-for="child of item.children"
                                        :key="child.id"
                                        @click="panelClick(child)"
                                    >
                                        {{ child.name }}
                                    </v-list-item>
                                </v-list-item-group>
                            </v-list>
                        </v-expansion-panel-content>
                    </v-expansion-panel>
                </v-expansion-panels>
            </v-card>
        </v-col>
        <v-col cols="12" lg="9" xs="12">
            <FlowChart v-show="showFlow" :source="source" />
            <TableList v-if="!showFlow" />
        </v-col>
    </v-row>
</template>

<script>
import AppConfig from '../../constants/AppConfig';
import FlowChart from './FlowChart.vue';
import TableList from './TableList.vue';

export default {
    name: 'ExpansionPanels',
    components: {
        FlowChart,
        TableList,
    },
    data() {
        return {
            sourceList: [
                { id: 1, name: '呈批件', children: [] },
                { id: 2, name: '收发文', children: [] },
                { id: 3, name: '综合办公', children: [] },
                { id: 4, name: '其他', children: [] },
            ],

            source: null,

            showFlow: false,
        };
    },
    methods: {
        async getSourceItem() {
            const userRole = localStorage
                .getItem(`${AppConfig.sys_code_prefix}-oa_role`)
                .split(',');

            const result = await this.$axios.get(
                '/api/be/workflow/source/list?status=1&page=0&size=999'
            );

            const itemList = result.rows;
            itemList.forEach((item) => {
                if (!item.role) {
                    switch (item.type) {
                        case 1:
                            this.sourceList[0].children.push(item);
                            break;
                        case 2:
                            this.sourceList[1].children.push(item);
                            break;
                        case 3:
                            this.sourceList[2].children.push(item);
                            break;
                        default:
                            this.sourceList[3].children.push(item);
                            break;
                    }
                } else if (userRole.includes(item.role)) {
                    switch (item.type) {
                        case 1:
                            this.sourceList[0].children.push(item);
                            break;
                        case 2:
                            this.sourceList[1].children.push(item);
                            break;
                        case 3:
                            this.sourceList[2].children.push(item);
                            break;
                        default:
                            this.sourceList[3].children.push(item);
                            break;
                    }
                }
            });
        },
        panelClick(item) {
            this.source = item;
            this.showFlow = true;
        },
    },

    mounted() {
        this.getSourceItem();
    },
};
</script>

<style scoped></style>
