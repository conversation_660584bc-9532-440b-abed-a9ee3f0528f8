<template>
    <v-card>
        <v-card-title>
            <v-spacer>
                <v-btn color="primary" @click="refresh">返回</v-btn>
                <v-btn
                    :to="`/${sysCodePrefix}_flow/flow_${this.source?.id}?sourceid=${this.source?.id}`"
                    class="float-right"
                    color="primary"
                    >发起流程
                </v-btn>
            </v-spacer>
        </v-card-title>
        <v-card-text>
            <VueBpmn
                :options="bpmnOptions"
                :url="bpmnFileUrl"
                style="height: 500px"
            />
        </v-card-text>
    </v-card>
</template>

<script>
import VueBpmn from 'vue-bpmn';
import AppConfig from '../../constants/AppConfig';

export default {
    name: 'FlowChart',
    props: ['source'],
    components: {
        VueBpmn,
    },
    data() {
        return {
            bpmnFileUrl: '',
            bpmnOptions: {
                additionalModules: [
                    {
                        zoomScroll: ['value', ''],
                        bendpoints: ['value', ''],
                    },
                ],
            },
            sysCodePrefix: AppConfig.sys_code_prefix,
        };
    },
    methods: {
        refresh() {
            window.location.reload();
        },
    },
    watch: {
        source(source) {
            this.bpmnFileUrl = `${AppConfig.oaFileServer}${
                source?.source
            }?t=${Date.now()}`;
        },
    },
};
</script>

<style scoped></style>
