<template>
    <div>
        <v-card class="pa-4">
            <v-tabs v-if="!$vuetify.breakpoint.xs" v-model="tab" show-arrows>
                <v-tab v-for="item in tabList" :key="item.id"
                    >{{ item.text }}
                </v-tab>
            </v-tabs>
            <MattersList v-if="tab === 0" />
            <ReadList v-if="tab === 1" />
            <CreatedByMeList v-if="tab === 2" />
            <JoinedList v-if="tab === 3" />
            <!--        <SubSystemList v-if="tab === 4" />-->
        </v-card>
        <v-footer v-if="$vuetify.breakpoint.xs" fixed padless>
            <v-tabs v-model="tab" centered grow>
                <v-tab v-for="item in tabList" :key="item.id"
                    >{{ item.text }}
                </v-tab>
            </v-tabs>
        </v-footer>
    </div>
</template>

<script>
import MattersList from './tableList/MattersList.vue';
import ReadList from './tableList/ReadList.vue';
import CreatedByMeList from './tableList/CreatedByMeList.vue';
import JoinedList from './tableList/JoinedList.vue';
import SubSystemList from './tableList/SubSystemList.vue';

export default {
    name: 'TableList',
    components: {
        MattersList,
        ReadList,
        CreatedByMeList,
        JoinedList,
        SubSystemList,
    },
    data() {
        return {
            tab: 0,
            tabList: [
                { id: 0, text: '待办' },
                { id: 1, text: '阅知' },
                { id: 2, text: '我的' },
                { id: 3, text: '经办' },
                // { id: 4, text: '子系统' },
            ],
        };
    },
};
</script>

<style scoped></style>
