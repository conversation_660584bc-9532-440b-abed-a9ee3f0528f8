<template>
    <div>
        <v-btn class="mr-4" color="primary" small @click="transferDialog = true"
            >转交
        </v-btn>

        <v-dialog v-model="transferDialog" scrollable width="50vw">
            <v-card min-height="75vh">
                <v-card-title>转交</v-card-title>
                <v-card-text>
                    <v-row>
                        <v-col>
                            <v-text-field
                                v-model="searchContent"
                                label="查找用户"
                            >
                            </v-text-field>
                        </v-col>
                        <v-col></v-col>
                    </v-row>
                    <v-row>
                        <v-col>
                            <v-treeview
                                v-model="transferSelected"
                                :items="departments"
                                :search="searchContent"
                                dense
                                item-key="id"
                                item-text="fullName"
                                open-on-click
                                return-object
                                selectable
                            ></v-treeview>
                        </v-col>
                        <v-col>
                            <v-list style="height: 100%; overflow-y: auto">
                                <v-list-item-title>已选择</v-list-item-title>
                                <v-list-item
                                    v-for="item in transferSelected"
                                    :key="item.id"
                                    dense
                                >
                                    <v-list-item-avatar>
                                        <img :src="item.avatar" alt="avatar" />
                                    </v-list-item-avatar>
                                    {{ item.fullName }}
                                </v-list-item>
                            </v-list>
                        </v-col>
                    </v-row>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" @click="transfer">提交</v-btn>
                    <v-btn @click="transferDialog = false">取消</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import {
    nodeAppend,
    nodeApprove,
    getAllUser,
    getAllDepartment,
} from '../../../helper/APIService';
import { formatDepList } from '../../../helper/formatData';
import { localUserInfo } from '../../../helper/localUserInfo';

export default {
    name: 'TransferCreate',
    props: ['formId', 'nodeId', 'nodeTitle', 'sourceNodeId', 'workflowId'],
    data() {
        return {
            transferDialog: false,

            searchContent: '',

            transferSelected: [],
            transferList: [],

            departments: [],
        };
    },

    methods: {
        async transfer() {
            const uids = this.transferSelected
                .map((item) => {
                    return item.id;
                })
                .toString();

            if (!uids) {
                return;
            }

            await nodeAppend({
                axios: this.$axios,
                formId: this.formId,
                nodeId: this.nodeId,
                selectedNodeName: this.nodeTitle,
                sourceNodeId: this.sourceNodeId,
                userId: uids,
                workflowId: this.workflowId,
            });

            await nodeApprove({ axios: this.$axios, nodeId: this.nodeId });

            await this.$router.push('/processCenter');
        },

        async initTransferList() {
            const { userList } = await getAllUser(this.$axios);
            const { departmentList } = await getAllDepartment({
                axios: this.$axios,
                companyId: localUserInfo.company.id,
            });

            this.departments = formatDepList(
                userList,
                departmentList
            ).mixedUserAndDepList;
        },
    },

    mounted() {
        this.initTransferList();
    },
};
</script>

<style scoped></style>
