<template>
    <div>
        <v-btn class="mr-2" color="primary" small @click="jointConfirm"
            >提交
        </v-btn>
        <v-dialog v-model="jointConfirmDialog" max-width="600px">
            <v-card>
                <v-card-title>请选择下一节点</v-card-title>
                <v-card-text>
                    <v-form>
                        <v-row>
                            <v-col>
                                <v-select
                                    v-model="nodeSelected"
                                    :items="nodeList"
                                    :rules="[rules.required]"
                                    item-text="name"
                                    label="请选择下一节点"
                                    return-object
                                    @change="nodeChanged"
                                ></v-select>
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                <v-select
                                    v-model="approverSelected"
                                    :items="approverList"
                                    :rules="[rules.required]"
                                    item-text="fullName"
                                    label="选择审批人"
                                    return-object
                                ></v-select>
                            </v-col>
                        </v-row>
                    </v-form>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" @click="submitJoint">提交</v-btn>
                    <v-btn @click="jointConfirmDialog = false">取消</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import {
    formModify,
    getNodeBySourceNodeId,
    getUserByNodeRole,
    nodeAppend,
    nodeApprove,
} from '../../../helper/APIService';

export default {
    name: 'JointConfirm',
    props: ['formId', 'formData', 'nodeId', 'sourceNodeId'],
    data() {
        return {
            jointConfirmDialog: false,

            nodeList: [],
            nodeSelected: '',

            approverList: [],
            approverSelected: '',

            rules: {
                required: (value) => !!value || '此条目不能为空',
            },
        };
    },
    methods: {
        async jointConfirm() {
            await formModify({
                axios: this.$axios,
                formData: this.formData,
                formId: this.formId,
            });

            const { nodeList } = getNodeBySourceNodeId({
                axios: this.$axios,
                sourceNodeId: this.sourceNodeId,
            });
            this.nodeList = nodeList;

            this.jointConfirmDialog = true;
        },
        async nodeChanged() {
            const { userList } = await getUserByNodeRole({
                axios: this.$axios,
                role: this.nodeSelected.role,
            });
            this.approverList = userList;

            if (this.approverList.length === 1) {
                [this.approverSelected] = this.approverList;
            }
        },
        async submitJoint() {
            await nodeAppend({
                axios: this.$axios,
                formId: this.formId,
                nodeId: this.nodeId,
                selectedNodeName: this.nodeSelected.name,
                sourceNodeId: this.nodeSelected.id,
                userId: this.approverSelected.id,
                workflowId: this.workflowId,
            });

            await nodeApprove({ axios: this.$axios, nodeId: this.nodeId });

            this.jointConfirmDialog = false;
            await this.$router.push('/processCenter');
        },
    },
};
</script>

<style scoped></style>
