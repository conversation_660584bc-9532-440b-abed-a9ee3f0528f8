<template>
    <div>
        <v-btn class="mr-4" color="primary" small @click="staging">暂存</v-btn>
    </div>
</template>

<script>
import { formModify } from '../../../helper/APIService';

export default {
    name: 'StagingCreate',
    props: ['currentComment', 'formData', 'formId', 'jobId'],
    methods: {
        async staging() {
            await formModify({
                axios: this.$axios,
                formData: this.formData,
                formId: this.formId,
            });

            const jobModify = await this.$axios.post('/api/be/oa/job/modify', {
                id: this.jobId,
                extra: 'staging',
                result: this.currentComment,
            });

            if (jobModify.state === 'error') {
                alert(jobModify.errorMsg);
                return;
            }

            await this.$router.push('/processCenter');
        },
    },
};
</script>

<style scoped></style>
