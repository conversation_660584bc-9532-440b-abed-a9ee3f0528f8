<template>
    <div>
        <v-btn
            v-if="isAdmin"
            class="mr-2"
            color="primary"
            small
            @click="adminSubmitting"
            >跳转
        </v-btn>

        <v-dialog v-model="adminSubmitDialog" max-width="600px">
            <v-card>
                <v-card-title>请选择跳转节点</v-card-title>
                <v-card-text>
                    <v-form>
                        <v-row>
                            <v-col>
                                <v-select
                                    v-model="adminNodeSelected"
                                    :items="adminNodeList"
                                    :rules="[rules.required]"
                                    item-text="name"
                                    label="请选择跳转节点"
                                    return-object
                                    @change="adminNodeChanged"
                                ></v-select>
                            </v-col>
                        </v-row>
                        <v-row v-if="adminNodeSelected.name !== '发起'">
                            <v-col>
                                <v-select
                                    v-model="adminApproverSelected"
                                    :items="adminApproverList"
                                    :rules="[rules.required]"
                                    item-text="fullName"
                                    label="选择节点操作人"
                                    return-object
                                ></v-select>
                            </v-col>
                        </v-row>
                    </v-form>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" @click="adminSubmitNode">提交</v-btn>
                    <v-btn @click="adminSubmitDialog = false">取消</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog v-model="adminPromoterDialog" max-width="600px" scrollable>
            <v-card min-height="50vh">
                <v-card-title>选择承办人</v-card-title>
                <v-card-text>
                    <v-row>
                        <v-col>
                            <v-text-field
                                v-model="adminPromoterSearchContent"
                                label="查找用户"
                            >
                            </v-text-field>
                        </v-col>
                        <v-col></v-col>
                    </v-row>
                    <v-row>
                        <v-col>
                            <v-treeview
                                v-model="adminPromoterSelected"
                                :items="adminPromoterDepartments"
                                :search="adminPromoterSearchContent.trim()"
                                dense
                                item-key="id"
                                item-text="fullName"
                                open-all
                                return-object
                                selectable
                            ></v-treeview>
                        </v-col>
                        <v-col>
                            <v-list style="height: 100%; overflow-y: auto">
                                <v-list-item-title>已选择</v-list-item-title>
                                <v-list-item
                                    v-for="item in adminPromoterSelected"
                                    :key="item.id"
                                    dense
                                >
                                    <v-list-item-avatar>
                                        <img :src="item.avatar" alt="avatar" />
                                    </v-list-item-avatar>
                                    {{ item.fullName }}
                                </v-list-item>
                            </v-list>
                        </v-col>
                    </v-row>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" @click="adminSubmitNode">提交</v-btn>
                    <v-btn @click="adminPromoterDialog = false">取消</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import {
    adminGetAllNode,
    adminNodeAppend,
    adminNodeApprove,
} from '../../../helper/adminAPIService';
import {
    getAllDepartment,
    getAllUser,
    getUserByNodeRole,
} from '../../../helper/APIService';
import { formatDepList } from '../../../helper/formatData';
import { localUserInfo, localUserRole } from '../../../helper/localUserInfo';

export default {
    name: 'AdminJump',
    props: ['formCreatorId', 'formId', 'nodeId', 'sourceId', 'workflowId'],
    data() {
        return {
            isAdmin: false,

            adminSubmitDialog: false,
            adminPromoterDialog: false,

            adminPromoterSearchContent: '',
            adminPromoterDepartments: '',
            adminPromoterSelected: [],

            rules: {
                required: (value) => !!value || '此条目不能为空',
            },

            adminNodeList: [],
            adminNodeSelected: {},
            adminApproverList: [],
            adminApproverSelected: {},
        };
    },
    methods: {
        async adminInitPromoterList() {
            const { userList } = await getAllUser(this.$axios);
            const { departmentList } = await getAllDepartment({
                axios: this.$axios,
                companyId: localUserInfo.company.id,
            });

            this.adminPromoterDepartments = formatDepList(
                userList,
                departmentList
            ).mixedUserAndDepList;
        },

        async adminSubmitting() {
            const { nodeList } = await adminGetAllNode({
                axios: this.$axios,
                sourceId: this.sourceId,
            });

            this.adminNodeList = nodeList;

            this.adminSubmitDialog = true;
        },

        async adminNodeChanged() {
            if (this.adminNodeSelected.name === '承办人') {
                await this.adminInitPromoterList();
                this.adminPromoterDialog = true;
            } else if (this.adminNodeSelected.name === '发起') {
                return;
            } else {
                const { userList } = await getUserByNodeRole({
                    axios: this.$axios,
                    role: this.adminNodeSelected.role,
                });

                this.adminApproverList = userList;
            }
        },

        async adminSubmitNode() {
            if (this.adminPromoterSelected.length !== 0) {
                [this.adminApproverSelected] = this.adminPromoterSelected;
            }

            if (this.adminNodeSelected.name === '发起') {
                this.adminApproverSelected.id = this.formCreatorId;
            }

            await adminNodeAppend({
                axios: this.$axios,
                formId: this.formId,
                nodeId: this.nodeId,
                selectedNodeName: this.adminNodeSelected.name,
                sourceNodeId: this.adminNodeSelected.id,
                userId: this.adminApproverSelected.id,
                workflowId: this.workflowId,
            });

            await adminNodeApprove({ axios: this.$axios, nodeId: this.nodeId });

            this.adminSubmitDialog = false;
            this.adminPromoterDialog = false;

            await this.$router.push('/processCenter');
        },
    },
    mounted() {
        this.isAdmin = localUserRole.includes('ROLE_ADMIN');
    },
    watch: {
        adminPromoterSelected(newValue) {
            if (newValue.length > 1) {
                this.promoterSelected.shift();
            }
        },
    },
};
</script>
