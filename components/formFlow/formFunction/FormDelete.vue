<template>
    <div>
        <v-btn class="mr-2" color="error" small @click="deleteDialog = true"
            >删除
        </v-btn>
        <v-dialog v-model="deleteDialog" max-width="600px">
            <v-card>
                <v-card-title>确认删除?</v-card-title>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="error" @click="deleteWorkflow">确认</v-btn>
                    <v-btn @click="deleteDialog = false">取消</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { workflowDelete } from '../../../helper/APIService';

export default {
    name: 'FormDelete',
    props: ['workflowId'],
    data() {
        return {
            deleteDialog: false,
        };
    },
    methods: {
        async deleteWorkflow() {
            await workflowDelete({
                axios: this.$axios,
                workflowId: this.workflowId,
            });
            await this.$router.push('/processCenter');
        },
    },
};
</script>

<style scoped></style>
