<template>
    <v-btn
        v-if="userIsCreator"
        class="mr-2"
        color="error"
        small
        @click="recallWorkflow"
        >撤回
    </v-btn>
</template>

<script>
import { workflowReset } from '../../../helper/APIService';
import { localUserInfo } from '../../../helper/localUserInfo';

export default {
    name: 'RecallCreate',
    props: ['formCreatorId', 'workflowId'],
    data() {
        return {
            userIsCreator: false,
        };
    },
    methods: {
        async recallWorkflow() {
            await workflowReset({
                axios: this.$axios,
                workflowId: this.workflowId,
            });
            await this.$router.push('/processCenter');
        },
    },

    mounted() {
        this.userIsCreator = this.formCreatorId === localUserInfo.id;
    },
};
</script>

<style scoped></style>
