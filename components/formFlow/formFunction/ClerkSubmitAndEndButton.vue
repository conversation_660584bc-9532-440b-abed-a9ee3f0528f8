<template>
    <div>
        <v-btn color="error" small @click="submitAndEndDialog = true"
            >提交并结束
        </v-btn>

        <v-snackbar
            v-model="snackbar"
            centered
            color="red"
            outlined
            text
            timeout="3000"
            >请输入批示内容
        </v-snackbar>

        <v-dialog v-model="submitAndEndDialog" max-width="600px">
            <v-card>
                <v-card-title>是否提交批示并结束流程</v-card-title>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="error" @click="submitAndEnd">确认</v-btn>
                    <v-btn @click="submitAndEndDialog = false">取消</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { jobModify, nodeApprove } from '../../../helper/APIService';
import syncFormJobTime from '../../../helper/syncFormJobTime';

export default {
    name: 'ClerkSubmitAndEndButton',
    props: [
        'currentComment',
        'currentCommentField',
        'formData',
        'formId',
        'jobId',
        'nodeId',
    ],
    data: () => ({
        snackbar: false,
        submitAndEndDialog: false,
    }),
    methods: {
        async submitAndEnd() {
            if (!this.currentComment.trim()) {
                this.submitAndEndDialog = false;
                this.snackbar = true;
                return;
            }

            await jobModify({
                axios: this.$axios,
                jobId: this.jobId,
                userComment: this.currentComment,
            });

            await nodeApprove({ axios: this.$axios, nodeId: this.nodeId });

            await syncFormJobTime({
                axios: this.$axios,
                currentComment: this.currentComment,
                currentCommentField: this.currentCommentField,
                formData: this.formData,
                formId: this.formId,
                jobId: this.jobId,
            });

            this.submitAndEndDialog = false;
            await this.$router.push('/processCenter');
        },
    },
};
</script>
