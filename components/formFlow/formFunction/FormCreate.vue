<template>
    <div>
        <v-snackbar
            v-model="snackbar"
            centered
            color="red"
            outlined
            text
            timeout="1500"
            >请填写必填项
        </v-snackbar>

        <v-btn class="mr-2" color="primary" small @click="submit">提交</v-btn>
        <v-btn class="mr-2" color="primary" small @click="jointDrafting"
            >联合起草
        </v-btn>

        <v-dialog v-model="submitDialog" max-width="600px">
            <v-card>
                <v-card-title>请选择下一节点</v-card-title>
                <v-card-text>
                    <v-form>
                        <v-row>
                            <v-col>
                                <v-select
                                    v-model="nodeSelected"
                                    :items="nodeList"
                                    :rules="[rules.required]"
                                    item-text="name"
                                    label="请选择下一节点"
                                    return-object
                                    @change="nodeChanged"
                                ></v-select>
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col>
                                <v-select
                                    v-model="approverSelected"
                                    :items="approverList"
                                    :rules="[rules.required]"
                                    item-text="fullName"
                                    label="选择审批人"
                                    return-object
                                ></v-select>
                            </v-col>
                        </v-row>
                    </v-form>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" @click="submitNode">提交</v-btn>
                    <v-btn @click="submitDialog = false">取消</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog v-model="jointDialog" max-width="600px">
            <v-card>
                <v-card-title>请选择联合起草人</v-card-title>
                <v-card-text>
                    <v-form>
                        <v-row>
                            <v-col>
                                <v-select
                                    v-model="jointSelected"
                                    :items="jointUsers"
                                    :rules="[rules.required]"
                                    item-text="fullName"
                                    label="请选择联合起草人"
                                    return-object
                                ></v-select>
                            </v-col>
                        </v-row>
                    </v-form>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" @click="submitJointDrafting"
                        >发起联合起草
                    </v-btn>
                    <v-btn @click="jointDialog = false">取消</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import {
    formCreate,
    formModify,
    getJointUserByCompanyId,
    getNodeBySourceNodeId,
    getUserByNodeRole,
    jointDraftingCreate,
    nodeAppend,
    nodeApprove,
    workflowCreate,
    workflowModify,
} from '../../../helper/APIService';
import { localUserInfo } from '../../../helper/localUserInfo';

export default {
    name: 'FormCreate',
    props: [
        'formData',
        'formIdProp',
        'jobIdProp',
        'nodeIdProp',
        'sourceNodeIdProp',
        'workflowIdProp',
    ],
    data() {
        return {
            sourceId: '',

            workflowId: '',
            nodeId: '',
            sourceNodeId: '',
            jobId: '',
            formId: '',

            nodeList: [],
            nodeSelected: '',
            approverList: [],
            approverSelected: '',

            jointUsers: [],
            jointSelected: '',

            submitDialog: false,
            jointDialog: false,

            snackbar: false,

            rules: {
                required: (value) => !!value || '此条目不能为空',
            },
        };
    },
    methods: {
        async createWF() {
            const { jobId, nodeId, sourceNodeId, workflowId } =
                await workflowCreate({
                    axios: this.$axios,
                    sourceId: this.sourceId,
                    workflowTitle: this.formData.title,
                });
            this.jobId = jobId;
            this.nodeId = nodeId;
            this.sourceNodeId = sourceNodeId;
            this.workflowId = workflowId;

            const { formId } = await formCreate({
                axios: this.$axios,
                workflowId: this.workflowId,
                formData: this.formData,
            });
            this.formId = formId;
        },

        // submit button
        async submit() {
            if (!this.formData.title.trim()) {
                this.snackbar = true;
                return;
            }

            if (this.sourceId === '3001' || this.sourceId === '3015') {
                if (
                    !this.formData.docType.trim() ||
                    !this.formData.docDep.trim() ||
                    !this.formData.docNumber.trim() ||
                    this.formData.docCategory.length < 1
                ) {
                    this.snackbar = true;
                    return;
                }
            }

            if (this.sourceId && !this.workflowId) {
                await this.createWF();
            } else if (this.sourceId && this.workflowId) {
                await workflowModify({
                    axios: this.$axios,
                    workflowId: this.workflowId,
                    workflowTitle: this.formData.title,
                });

                await formModify({
                    axios: this.$axios,
                    formId: this.formId,
                    formData: this.formData,
                });
            } else {
                this.workflowId = this.workflowIdProp;
                this.formId = this.formIdProp;
                this.nodeId = this.nodeIdProp;
                this.sourceNodeId = this.sourceNodeIdProp;

                await formModify({
                    axios: this.$axios,
                    formId: this.formId,
                    formData: this.formData,
                });

                await workflowModify({
                    axios: this.$axios,
                    workflowId: this.workflowId,
                    workflowTitle: this.formData.title,
                });
            }

            const { nodeList } = await getNodeBySourceNodeId({
                axios: this.$axios,
                sourceNodeId: this.sourceNodeId,
            });
            this.nodeList = nodeList;

            this.submitDialog = true;
        },

        async nodeChanged() {
            const { userList } = await getUserByNodeRole({
                axios: this.$axios,
                role: this.nodeSelected.role,
            });
            this.approverList = userList;
        },

        async submitNode() {
            if (!this.nodeSelected || !this.approverSelected) {
                this.snackbar = true;
                return;
            }

            await nodeAppend({
                axios: this.$axios,
                formId: this.formId,
                nodeId: this.nodeId,
                selectedNodeName: this.nodeSelected.name,
                sourceNodeId: this.nodeSelected.id,
                userId: this.approverSelected.id,
                workflowId: this.workflowId,
            });

            await nodeApprove({ axios: this.$axios, nodeId: this.nodeId });

            this.submitDialog = false;
            await this.$router.push('/processCenter');
        },

        async jointDrafting() {
            const { userList } = await getJointUserByCompanyId({
                axios: this.$axios,
                companyId: localUserInfo.company.id,
            });

            this.jointUsers = userList;
            this.jointDialog = true;
        },

        async submitJointDrafting() {
            if (this.sourceId && !this.workflowId) {
                await this.createWF();
            } else if (this.sourceId && this.workflowId) {
                await formModify({
                    axios: this.$axios,
                    formId: this.formId,
                    formData: this.formData,
                });
            } else {
                this.workflowId = this.workflowIdProp;
                this.nodeId = this.nodeIdProp;
                this.jobId = this.jobIdProp;

                await formModify({
                    axios: this.$axios,
                    formId: this.formId,
                    formData: this.formData,
                });
            }

            await jointDraftingCreate({
                axios: this.$axios,
                workflowId: this.workflowId,
                jobId: this.jobId,
                nodeId: this.nodeId,
                userId: this.jointSelected.id,
            });

            this.jointDialog = false;
            await this.$router.push('/processCenter');
        },
    },
    mounted() {
        this.sourceId = this.$route.query.sourceid;
    },
};
</script>

<style scoped></style>
