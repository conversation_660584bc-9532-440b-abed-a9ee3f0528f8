<template>
    <div>
        <v-btn
            class="mr-4"
            color="primary"
            small
            @click="
                currentComment
                    ? (countersignEndDialog = true)
                    : (snackbar = true)
            "
            >提交
        </v-btn>

        <v-snackbar
            v-model="snackbar"
            centered
            color="red"
            outlined
            text
            timeout="3000"
            >请输入批示内容
        </v-snackbar>

        <v-dialog v-model="countersignEndDialog" max-width="33vw">
            <v-card>
                <v-card-title class="text-center"> 是否确认提交？</v-card-title>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" @click="countersignEnding"
                        >确认</v-btn
                    >
                    <v-btn @click="countersignEndDialog = false">取消</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { countersignEnd } from '../../../helper/APIService';
import syncFormJobTime from '../../../helper/syncFormJobTime';

export default {
    name: 'CountersignEnd',
    props: [
        'currentComment',
        'currentCommentField',
        'formData',
        'formId',
        'jobId',
    ],
    data() {
        return {
            snackbar: false,
            countersignEndDialog: false,
        };
    },

    methods: {
        async countersignEnding() {
            if (!this.currentComment.trim()) {
                this.snackbar = true;
                return;
            }

            await countersignEnd({
                axios: this.$axios,
                jobId: this.jobId,
                userComment: this.currentComment,
            });

            await syncFormJobTime({
                axios: this.$axios,
                currentComment: this.currentComment,
                currentCommentField: this.currentCommentField,
                formData: this.formData,
                formId: this.formId,
                jobId: this.jobId,
            });

            this.countersignEndDialog = false;
            await this.$router.push('/processCenter');
        },
    },
};
</script>

<style scoped></style>
