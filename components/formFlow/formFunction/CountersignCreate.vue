<template>
    <div>
        <v-btn
            class="mr-4"
            color="primary"
            small
            @click="countersignDialog = true"
            >会签
        </v-btn>

        <v-dialog v-model="countersignDialog" max-width="600px" scrollable>
            <v-card min-height="75vh">
                <v-card-title>会签</v-card-title>
                <v-card-text>
                    <v-row>
                        <v-col>
                            <v-text-field
                                v-model="searchContent"
                                label="查找用户"
                            >
                            </v-text-field>
                        </v-col>
                        <v-col></v-col>
                    </v-row>
                    <v-row>
                        <v-col>
                            <v-treeview
                                v-model="countersignSelected"
                                :items="departments"
                                :search="searchContent.trim()"
                                dense
                                indeterminate-icon=""
                                item-key="id"
                                item-text="fullName"
                                off-icon=""
                                on-icon=""
                                open-all
                                return-object
                                selectable
                                selected-color=""
                            >
                                <template #label="{ item }">
                                    <div
                                        style="cursor: pointer"
                                        @click="onItemClick(item)"
                                    >
                                        {{ item.fullName }}
                                    </div>
                                </template>
                            </v-treeview>
                        </v-col>
                        <v-col>
                            <v-list style="height: 100%; overflow-y: auto">
                                <v-list-item-title>已选择</v-list-item-title>
                                <v-list-item
                                    v-for="item in countersignSelected"
                                    :key="item.id"
                                    dense
                                    style="cursor: pointer"
                                    @click="onItemClick(item)"
                                >
                                    <v-list-item-avatar>
                                        <img :src="item.avatar" alt="avatar" />
                                    </v-list-item-avatar>
                                    <v-list-item-content>
                                        {{ item.fullName }}
                                    </v-list-item-content>
                                    <v-list-item-action>
                                        <v-icon>mdi-close</v-icon>
                                    </v-list-item-action>
                                </v-list-item>
                            </v-list>
                        </v-col>
                    </v-row>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" @click="countersigning">提交</v-btn>
                    <v-btn @click="countersignDialog = false">取消</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import {
    countersignCreate,
    getAllDepartment,
    getAllUser,
} from '../../../helper/APIService';
import syncFormJobTime from '../../../helper/syncFormJobTime';
import { formatDepList } from '../../../helper/formatData';
import { localUserInfo } from '../../../helper/localUserInfo';

export default {
    name: 'CountersignCreate',
    props: [
        'currentComment',
        'currentCommentField',
        'formData',
        'formId',
        'jobId',
        'nodeId',
        'workflowId',
    ],
    data() {
        return {
            countersignDialog: false,

            searchContent: '',

            countersignSelected: [],
            countersignList: [],

            departments: [],
        };
    },

    methods: {
        async countersigning() {
            const uids = this.countersignSelected
                .map((item) => {
                    return item.id;
                })
                .toString();

            if (!uids) return;

            await countersignCreate({
                axios: this.$axios,
                jobId: this.jobId,
                nodeId: this.nodeId,
                uids,
                userComment: this.currentComment,
                workflowId: this.workflowId,
            });

            await syncFormJobTime({
                axios: this.$axios,
                currentComment: this.currentComment,
                currentCommentField: this.currentCommentField,
                formData: this.formData,
                formId: this.formId,
                jobId: this.jobId,
            });

            await this.$router.push('/processCenter');
        },

        async initCountersignList() {
            const { userList } = await getAllUser(this.$axios);
            const { departmentList } = await getAllDepartment({
                axios: this.$axios,
                companyId: localUserInfo.company.id,
            });

            this.departments = formatDepList(
                userList,
                departmentList
            ).mixedUserAndDepList;
        },

        onItemClick(item) {
            if (this.countersignSelected.includes(item)) {
                this.countersignSelected = this.countersignSelected.filter(
                    (i) => i.id !== item.id
                );
                return;
            }
            this.countersignSelected.push(item);
        },
    },

    mounted() {
        this.initCountersignList();
    },
};
</script>

<style scoped></style>
