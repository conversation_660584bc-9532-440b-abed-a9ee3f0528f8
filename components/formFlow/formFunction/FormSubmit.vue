<template>
    <div>
        <v-btn class="mr-4" color="primary" small @click.once="submitting"
            >提交
        </v-btn>

        <v-snackbar
            v-model="snackbar"
            centered
            color="red"
            outlined
            text
            timeout="3000"
            >请输入批示内容
        </v-snackbar>

        <v-dialog v-model="promoterDialog" max-width="600px" scrollable>
            <v-card min-height="50vh">
                <v-card-title>选择承办人</v-card-title>
                <v-card-text>
                    <v-row>
                        <v-col>
                            <v-text-field
                                v-model="promoterSearchContent"
                                label="查找用户"
                            >
                            </v-text-field>
                        </v-col>
                        <v-col></v-col>
                    </v-row>
                    <v-row>
                        <v-col>
                            <v-treeview
                                v-model="promoterSelected"
                                :items="promoterDepartments"
                                :search="promoterSearchContent.trim()"
                                dense
                                item-key="id"
                                item-text="fullName"
                                open-all
                                return-object
                                selectable
                            ></v-treeview>
                        </v-col>
                        <v-col>
                            <v-list style="height: 100%; overflow-y: auto">
                                <v-list-item-title>已选择</v-list-item-title>
                                <v-list-item
                                    v-for="item in promoterSelected"
                                    :key="item.id"
                                    dense
                                >
                                    <v-list-item-avatar>
                                        <img :src="item.avatar" alt="avatar" />
                                    </v-list-item-avatar>
                                    {{ item.fullName }}
                                </v-list-item>
                            </v-list>
                        </v-col>
                    </v-row>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" @click="submitNode">提交</v-btn>
                    <v-btn @click="promoterDialog = false">取消</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog v-model="submitDialog" max-width="600px">
            <v-card>
                <v-card-title>请选择下一节点</v-card-title>
                <v-card-text>
                    <v-form>
                        <v-row>
                            <v-col>
                                <v-select
                                    v-model="nodeSelected"
                                    :items="nodeList"
                                    :rules="[rules.required]"
                                    item-text="name"
                                    label="请选择下一节点"
                                    return-object
                                    @change="nodeChanged"
                                ></v-select>
                            </v-col>
                        </v-row>
                        <v-row v-if="nodeSelected.name !== '结束'">
                            <v-col>
                                <v-select
                                    v-model="approverSelected"
                                    :items="approverList"
                                    :rules="[rules.required]"
                                    item-text="fullName"
                                    label="选择审批人"
                                    return-object
                                ></v-select>
                            </v-col>
                        </v-row>
                    </v-form>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        v-if="nodeSelected.name !== '结束'"
                        color="primary"
                        @click="submitNode"
                        >提交
                    </v-btn>
                    <v-btn
                        v-if="nodeSelected.name === '结束'"
                        color="error"
                        @click="endingFlow"
                        >结束
                    </v-btn>
                    <v-btn @click="submitDialog = false">取消</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import {
    getAllDepartment,
    getAllUser,
    getNodeBySourceNodeId,
    getUserByNodeRole,
    jobModify,
    nodeAppend,
    nodeApprove,
} from '../../../helper/APIService';
import { formatDepList } from '../../../helper/formatData';
import syncFormJobTime from '../../../helper/syncFormJobTime';
import { localUserInfo } from '../../../helper/localUserInfo';

export default {
    name: 'FormSubmit',
    props: [
        'currentComment',
        'currentCommentField',
        'formCreatorId',
        'formData',
        'formId',
        'jobId',
        'nodeId',
        'sourceNodeId',
        'uidIsCreator',
        'workflowId',
    ],
    data() {
        return {
            submitDialog: false,
            promoterDialog: false,
            snackbar: false,

            promoterSearchContent: '',
            promoterDepartments: '',
            promoterSelected: [],

            rules: {
                required: (value) => !!value || '此条目不能为空',
            },

            nodeList: [],
            nodeSelected: {},
            approverList: [],
            approverSelected: {},
        };
    },
    methods: {
        async initPromoterList() {
            const { userList } = await getAllUser(this.$axios);
            const { departmentList } = await getAllDepartment({
                axios: this.$axios,
                companyId: localUserInfo.company.id,
            });

            this.promoterDepartments = formatDepList(
                userList,
                departmentList
            ).mixedUserAndDepList;
        },

        async submitting() {
            if (!this.currentComment.trim()) {
                this.snackbar = true;
                return;
            }
            const { nodeList } = await getNodeBySourceNodeId({
                axios: this.$axios,
                sourceNodeId: this.sourceNodeId,
            });

            this.nodeList = nodeList;

            if (
                this.nodeList.length === 1 &&
                this.nodeList[0].name === '承办人' &&
                !this.uidIsCreator
            ) {
                await this.initPromoterList();
                [this.nodeSelected] = this.nodeList;
                this.promoterDialog = true;
                return;
            }

            this.submitDialog = true;
        },

        async nodeChanged() {
            if (
                this.nodeSelected.name === '承办人' ||
                this.nodeSelected.name === '校对' ||
                this.nodeSelected.name === '加签子公司'
            ) {
                await this.initPromoterList();
                [this.nodeSelected] = this.nodeList;
                this.promoterDialog = true;
            } else {
                const { userList } = await getUserByNodeRole({
                    axios: this.$axios,
                    role: this.nodeSelected.role,
                });

                this.approverList = userList;
            }

            if (this.approverList.length === 1) {
                [this.approverSelected] = this.approverList;
            }
        },

        async submitNode() {
            if (this.promoterSelected.length !== 0) {
                [this.approverSelected] = this.promoterSelected;
            }

            if (!this.approverSelected || !this.nodeSelected) {
                return;
            }

            await jobModify({
                axios: this.$axios,
                jobId: this.jobId,
                userComment: this.currentComment,
            });

            await nodeAppend({
                axios: this.$axios,
                formId: this.formId,
                nodeId: this.nodeId,
                selectedNodeName: this.nodeSelected.name,
                sourceNodeId: this.nodeSelected.id,
                userId: this.approverSelected.id,
                workflowId: this.workflowId,
            });

            await nodeApprove({ axios: this.$axios, nodeId: this.nodeId });

            await syncFormJobTime({
                axios: this.$axios,
                currentComment: this.currentComment,
                currentCommentField: this.currentCommentField,
                formData: this.formData,
                formId: this.formId,
                jobId: this.jobId,
            });

            this.submitDialog = false;
            this.promoterDialog = false;
            await this.$router.push('/processCenter');
        },

        async endingFlow() {
            await jobModify({
                axios: this.$axios,
                jobId: this.jobId,
                userComment: this.currentComment,
            });

            await nodeApprove({
                axios: this.$axios,
                nodeId: this.nodeId,
                is_end: true,
            });

            await syncFormJobTime({
                axios: this.$axios,
                currentComment: this.currentComment,
                currentCommentField: this.currentCommentField,
                formData: this.formData,
                formId: this.formId,
                jobId: this.jobId,
            });

            this.submitDialog = false;
            this.promoterDialog = false;
            await this.$router.push('/processCenter');
        },
    },
    watch: {
        promoterSelected(newValue) {
            if (newValue.length > 1) {
                this.promoterSelected.shift();
            }
        },
    },
};
</script>
