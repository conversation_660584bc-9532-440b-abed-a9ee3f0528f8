<template>
    <div>
        <v-btn class="mr-4" color="error" small @click="endDialog = true"
            >结束
        </v-btn>

        <v-dialog v-model="endDialog" max-width="600px">
            <v-card>
                <v-card-title>是否结束流程</v-card-title>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="error" @click="endFlow">结束</v-btn>
                    <v-btn @click="endDialog = false">取消</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { nodeApprove } from '../../../helper/APIService';

export default {
    name: 'FormEnd',
    props: ['nodeId'],
    data() {
        return {
            endDialog: false,
        };
    },
    methods: {
        async endFlow() {
            await nodeApprove({ axios: this.$axios, nodeId: this.nodeId });

            this.endDialog = false;
            await this.$router.push('/processCenter');
        },
    },
};
</script>
