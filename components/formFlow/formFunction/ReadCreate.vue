<template>
    <div>
        <v-btn class="mr-2" color="primary" small @click="readDialog = true"
            >阅知
        </v-btn>

        <v-dialog v-model="readDialog" scrollable width="50vw">
            <v-card min-height="75vh">
                <v-card-title>阅知</v-card-title>
                <v-card-text>
                    <v-row>
                        <v-col>
                            <v-text-field
                                v-model="searchContent"
                                label="查找用户"
                            >
                            </v-text-field>
                        </v-col>
                        <v-col></v-col>
                    </v-row>
                    <v-row>
                        <v-col>
                            <v-treeview
                                v-model="readSelected"
                                :items="departments"
                                :search="searchContent.trim()"
                                dense
                                indeterminate-icon=""
                                item-key="id"
                                item-text="fullName"
                                off-icon=""
                                on-icon=""
                                open-all
                                return-object
                                selectable
                                selected-color=""
                            >
                                <template #label="{ item }">
                                    <div
                                        style="cursor: pointer"
                                        @click="onItemClick(item)"
                                    >
                                        {{ item.fullName }}
                                    </div>
                                </template>
                            </v-treeview>
                        </v-col>
                        <v-col>
                            <v-list style="height: 100%; overflow-y: auto">
                                <v-list-item-title>已选择</v-list-item-title>
                                <v-list-item
                                    v-for="item in readSelected"
                                    :key="item.id"
                                    dense
                                    style="cursor: pointer"
                                    @click="onItemClick(item)"
                                >
                                    <v-list-item-avatar>
                                        <img :src="item.avatar" alt="avatar" />
                                    </v-list-item-avatar>
                                    <v-list-item-content>
                                        {{ item.fullName }}
                                    </v-list-item-content>
                                    <v-list-item-action>
                                        <v-icon>mdi-close</v-icon>
                                    </v-list-item-action>
                                </v-list-item>
                            </v-list>
                        </v-col>
                    </v-row>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" @click="reading">提交</v-btn>
                    <v-btn @click="readDialog = false">取消</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import {
    getAllDepartment,
    getAllUser,
    readCreate,
} from '../../../helper/APIService';
import { formatDepList } from '../../../helper/formatData';
import { localUserInfo, localUserRole } from '../../../helper/localUserInfo';

export default {
    name: 'ReadCreate',
    props: ['currentComment', 'jobId', 'nodeId', 'workflowId'],
    data() {
        return {
            departments: [],
            readDialog: false,
            readSelected: [],
            role: localUserRole,
            searchContent: '',
        };
    },

    methods: {
        async reading() {
            const uids = this.readSelected
                .map((item) => {
                    return item.id;
                })
                .toString();

            if (!uids) {
                return;
            }

            await readCreate({
                axios: this.$axios,
                nodeId: this.nodeId,
                uids,
                workflowId: this.workflowId,
            });

            this.readDialog = false;
            await this.$router.push('/processCenter');
        },

        async initReadList() {
            const { userList } = await getAllUser(this.$axios);
            const { departmentList } = await getAllDepartment({
                axios: this.$axios,
                companyId: localUserInfo.company.id,
            });

            this.departments = formatDepList(
                userList,
                departmentList
            ).mixedUserAndDepList;
        },

        onItemClick(item) {
            if (this.readSelected.includes(item)) {
                this.readSelected = this.readSelected.filter(
                    (i) => i.id !== item.id
                );
                return;
            }
            this.readSelected.push(item);
        },
    },

    mounted() {
        this.initReadList();
    },
};
</script>

<style scoped></style>
