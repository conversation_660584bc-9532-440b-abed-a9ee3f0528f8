<template>
    <div>
        <v-btn class="mr-4" color="primary" small @click="getBackList"
            >退回
        </v-btn>

        <v-dialog v-model="backingDialog" max-width="600px">
            <v-card>
                <v-card-title> 请选择退回节点</v-card-title>
                <v-card-text>
                    <v-row>
                        <v-col>
                            <v-select
                                v-model="backNodeSelected"
                                :items="backNodeList"
                                item-text="title"
                                label="请选择退回节点"
                                return-object
                            ></v-select>
                        </v-col>
                    </v-row>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" @click="backing">提交</v-btn>
                    <v-btn @click="backingDialog = false">取消</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { nodeAppend, nodeApprove } from '../../../helper/APIService';

export default {
    name: 'BackingCreate',
    props: ['formCreatorId', 'formId', 'nodeId', 'nodeList', 'workflowId'],
    data() {
        return {
            backingDialog: false,

            rules: {
                required: (value) => !!value || '此条目不能为空',
            },

            backNodeList: [],
            backNodeSelected: '',
        };
    },

    methods: {
        async getBackList() {
            this.backNodeList = this.nodeList.slice(1, -1);
            this.backingDialog = true;
        },

        async backing() {
            if (this.backNodeSelected.title === '发起') {
                this.backNodeSelected.uid = this.formCreatorId;
            }

            await nodeAppend({
                axios: this.$axios,
                formId: this.formId,
                nodeId: this.nodeId,
                selectedNodeName: this.backNodeSelected.title,
                sourceNodeId: this.backNodeSelected.sourcenodeid,
                userId: this.backNodeSelected.uid,
                workflowId: this.workflowId,
            });

            await nodeApprove({ axios: this.$axios, nodeId: this.nodeId });

            this.backingDialog = false;
            await this.$router.push('/processCenter');
        },
    },
};
</script>

<style scoped></style>
