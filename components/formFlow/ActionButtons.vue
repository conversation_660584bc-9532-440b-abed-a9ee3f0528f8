<template>
    <div>
        <v-card-actions
            v-if="!isShowTimeLine && isEditable && jobInfo.actionType !== '3'"
        >
            <BackingCreate
                v-if="nodeInfo.returnCreate && jobInfo.actionType !== '2'"
                :formCreatorId="formInfo.formCreatorId"
                :formId="formInfo.formId"
                :nodeId="nodeInfo.nodeId"
                :nodeList="nodeInfo.nodeList"
                :workflowId="workflowId"
            />

            <CountersignCreate
                v-if="jobInfo.isReviewing && nodeInfo.countersignCreate"
                :currentComment="currentComment"
                :currentCommentField="currentCommentField"
                :formData="formInfo.formData"
                :formId="formInfo.formId"
                :jobId="jobInfo.jobId"
                :nodeId="nodeInfo.nodeId"
                :workflowId="workflowId"
            />

            <CountersignEnd
                v-if="jobInfo.isSigning"
                :currentComment="currentComment"
                :currentCommentField="currentCommentField"
                :formData="formInfo.formData"
                :formId="formInfo.formId"
                :jobId="jobInfo.jobId"
            />

            <FormCreate
                v-if="jobInfo.isCreating && jobInfo.actionType !== '5'"
                :formData="formInfo.formData"
                :formIdProp="formInfo.formId"
                :jobIdProp="jobInfo.jobId"
                :nodeIdProp="nodeInfo.nodeId"
                :sourceNodeIdProp="nodeInfo.sourceNodeId"
                :workflowIdProp="workflowId"
            />

            <FormDelete
                v-if="nodeInfo.nodeTitle === '发起' && isEditable"
                :workflowId="workflowId"
            />

            <FormEnd
                v-if="jobInfo.isEnding && isEditable"
                :nodeId="nodeInfo.nodeId"
            />

            <FormSubmit
                v-if="
                    jobInfo.isReviewing &&
                    !jobInfo.isEnding &&
                    nodeInfo.nodeTitle !== '文印员归档'
                "
                :currentComment="currentComment"
                :currentCommentField="currentCommentField"
                :formCreatorId="formInfo.formCreatorId"
                :formData="formInfo.formData"
                :formId="formInfo.formId"
                :jobId="jobInfo.jobId"
                :nodeId="nodeInfo.nodeId"
                :sourceNodeId="nodeInfo.sourceNodeId"
                :uidIsCreator="nodeInfo.uidIsCreator"
                :workflowId="workflowId"
            />

            <JointConfirm
                v-if="jobInfo.actionType === '5'"
                :formData="formInfo.formData"
                :formId="formInfo.formId"
                :nodeId="nodeInfo.nodeId"
                :sourceNodeId="nodeInfo.sourceNodeId"
            />

            <StagingCreate
                v-if="
                    nodeInfo.nodeTitle &&
                    nodeInfo.nodeTitle !== '发起' &&
                    !jobInfo.isEnding &&
                    nodeInfo.nodeTitle !== '文印员归档'
                "
                :currentComment="currentComment"
                :formData="formInfo.formData"
                :formId="formInfo.formId"
                :jobId="jobInfo.jobId"
            />

            <TransferCreate
                v-if="nodeInfo.transferCreate"
                :formId="formInfo.formId"
                :nodeId="nodeInfo.nodeId"
                :nodeTitle="nodeInfo.nodeTitle"
                :sourceNodeId="nodeInfo.sourceNodeId"
                :workflowId="workflowId"
            />

            <ClerkSubmitAndEndButton
                v-if="isClerk && nodeInfo.nodeTitle === '文印员归档'"
                :currentComment="currentComment"
                :currentCommentField="currentCommentField"
                :formData="formInfo.formData"
                :formId="formInfo.formId"
                :jobId="jobInfo.jobId"
                :nodeId="nodeInfo.nodeId"
            />
        </v-card-actions>
        <v-card-actions v-if="!isShowTimeLine">
            <AdminJump
                :formCreatorId="formInfo.formCreatorId"
                :formId="formInfo.formId"
                :nodeId="nodeInfo.nodeId"
                :sourceId="nodeInfo.sourceId"
                :workflowId="workflowId"
            />

            <ReadCreate
                v-if="jobInfo.actionType !== '3' && !jobInfo.isCreating"
                :nodeId="nodeInfo.nodeId"
                :workflowId="workflowId"
            />

            <RecallCreate
                v-if="nodeInfo.nodeTitle !== '发起'"
                :formCreatorId="formInfo.formCreatorId"
                :workflowId="workflowId"
            />
        </v-card-actions>
    </div>
</template>

<script>
import { localUserRole } from '../../helper/localUserInfo';

import AdminJump from './formFunction/AdminJump.vue';
import BackingCreate from './formFunction/BackingCreate.vue';
import CountersignCreate from './formFunction/CountersignCreate.vue';
import CountersignEnd from './formFunction/CountersignEnd.vue';
import FormCreate from './formFunction/FormCreate.vue';
import FormDelete from './formFunction/FormDelete.vue';
import FormEnd from './formFunction/FormEnd.vue';
import FormSubmit from './formFunction/FormSubmit.vue';
import JointConfirm from './formFunction/JointConfirm.vue';
import ReadCreate from './formFunction/ReadCreate.vue';
import RecallCreate from './formFunction/RecallCreate.vue';
import StagingCreate from './formFunction/StagingCreate.vue';
import TransferCreate from './formFunction/TransferCreate.vue';
import ClerkSubmitAndEndButton from './formFunction/ClerkSubmitAndEndButton.vue';

export default {
    name: 'ActionButtons',
    data() {
        return {
            isClerk: localUserRole.includes('ROLE_PRINTER_EDITOR'),
        };
    },
    props: [
        'currentComment',
        'currentCommentField',
        'isEditable',
        'formInfo',
        'jobInfo',
        'nodeInfo',
        'isShowTimeLine',
        'workflowId',
    ],
    components: {
        FormCreate,
        StagingCreate,
        FormSubmit,
        CountersignCreate,
        ReadCreate,
        TransferCreate,
        BackingCreate,
        CountersignEnd,
        AdminJump,
        FormDelete,
        RecallCreate,
        JointConfirm,
        FormEnd,
        ClerkSubmitAndEndButton,
    },

    async mounted() {
        if (this.jobInfo.actionType === '3') {
            await this.$axios.post('/api/be/oa/job/read', {
                id: this.jobInfo.jobId,
            });
        }
    },
};
</script>

<!--actionType: 1 -->
<!--actionType: 2 countersign-->
<!--actionType: 3 read-->
<!--actionType: 4 transfer-->
<!--actionType: 5 joint-->
