<template>
    <div style="flex: 1; display: flex">
        <div class="borderR">
            <span>{{ numberArray[9] }}</span>
        </div>
        <div class="borderR">
            <span>{{ numberArray[8] }}</span>
        </div>
        <div class="borderR">
            <span>{{ numberArray[7] }}</span>
        </div>
        <div class="borderR">
            <span>{{ numberArray[6] }}</span>
        </div>
        <div class="borderR">
            <span>{{ numberArray[5] }}</span>
        </div>
        <div class="borderR">
            <span>{{ numberArray[4] }}</span>
        </div>
        <div class="borderR">
            <span>{{ numberArray[3] }}</span>
        </div>
        <div class="borderR">
            <span>{{ numberArray[1] }}</span>
        </div>
        <div class="borderR">
            <span>{{ numberArray[0] }}</span>
        </div>
    </div>
</template>

<script>
export default {
    props: ['NumberProps'],
    name: 'AmountUnit',
    data() {
        return {
            numberArray: [],
        };
    },
    mounted() {
        if (typeof this.NumberProps === 'object') {
            this.numberArray = this.NumberProps;
            return;
        }
        this.numberArray = this.NumberProps?.split('').reverse() || [];
    },
    watch: {
        NumberProps(newValue) {
            this.numberArray = newValue.split('').reverse() || [];
        },
    },
};
</script>

<style scoped>
.borderR {
    border-right: 1px solid black;
    min-height: 32px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
