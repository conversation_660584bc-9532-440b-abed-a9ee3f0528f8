<template>
    <v-row>
        <v-col>
            <v-chip-group column>
                <v-chip
                    v-for="chip in chips"
                    :key="chip"
                    close
                    small
                    @click="setChipValue(chip)"
                    @click:close="deleteChip(chip)"
                    >{{ chip }}
                </v-chip>
            </v-chip-group>
        </v-col>
        <v-col style="display: flex">
            <v-text-field v-model="customChip" dense label="自定义">
            </v-text-field>
            <v-icon tag="a" @click="addChip(customChip)"> mdi-pen-plus</v-icon>
        </v-col>
    </v-row>
</template>

<script>
import { chipsGet, chipsSet } from '../../../helper/APIService';

export default {
    name: 'ReviewChips',
    props: ['getChipValue'],
    data() {
        return {
            chips: ['同意', '不同意', '拟同意', '请 阅示'],
            customChip: '',
        };
    },

    methods: {
        async initChips() {
            const {chips} = await chipsGet(this.$axios);
            if(chips.length>0 ){
                this.chips = chips;
            }
        },

        async addChip(customChip) {
            if (!customChip.trim()) return;
            const chips = `${this.chips.toString()},${customChip}`;
            await chipsSet({ axios: this.$axios, chips });

            await this.initChips();
        },

        async deleteChip(chip) {
            const remainChips = this.chips
                .filter((item) => {
                    return item !== chip;
                })
                .toString();

            await chipsSet({ axios: this.$axios, chips: remainChips });

            await this.initChips();
        },

        setChipValue(chip) {
            this.getChipValue(chip);
        },
    },
    mounted() {
        this.initChips();
    },
};
</script>

<style scoped></style>
