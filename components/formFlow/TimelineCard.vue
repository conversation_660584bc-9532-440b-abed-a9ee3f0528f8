<template>
    <div>
        <v-timeline dense>
            <v-timeline-item
                v-for="node in timelineList"
                :key="node.id"
                :color="color(node.status)"
                small
                style="border-bottom: #eeeeee 1px solid"
            >
                <div v-if="node.jobList.length <= 1">
                    {{ node.title }} : {{ node.user }}
                    {{ node.jobList[0]?.result }}&nbsp;&nbsp;{{
                        node.jobList[0]?.finish_time
                    }}
                </div>

                <div v-if="node.readList.length >= 1">
                    <v-btn
                        color="primary"
                        icon
                        @click="readListDialog(node.readList)"
                        >阅知
                    </v-btn>
                </div>

                <div v-if="node.jobList.length > 1">
                    <div>{{ node.title }}</div>
                    <v-timeline dense>
                        <v-timeline-item
                            v-for="job in node.jobList"
                            :key="job.id"
                            :color="color(job.action_type, job.status)"
                            dense
                            small
                        >
                            <v-row>
                                {{ job.action_type === '2' ? '会签:' : '' }}
                                {{ job.action_type === '4' ? '转交:' : '' }}
                                {{ job.User?.fullName }}
                                &nbsp;&nbsp;{{ job.result }}
                            </v-row>
                            <v-row>
                                {{ job.finish_time }}
                            </v-row>
                        </v-timeline-item>
                    </v-timeline>
                </div>
            </v-timeline-item>
        </v-timeline>
        <v-dialog v-model="readDialog" max-width="600px">
            <v-card class="pa-4">
                <v-card-text>
                    <v-row>
                        <v-chip-group
                            v-for="item in readList"
                            :key="item.id"
                            column
                        >
                            <v-chip
                                :color="item.is_read ? 'teal' : ''"
                                :style="item.is_read ? 'color:white' : ''"
                                small
                                tag="a"
                            >
                                {{ item.fromUser.fullName }}&nbsp;阅知至&nbsp;{{
                                    item.User.fullName
                                }}
                            </v-chip>
                        </v-chip-group>
                    </v-row>
                </v-card-text>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mixedNodeListAndJobList } from '../../helper/formatData';

export default {
    name: 'TimelineCard',
    props: ['jobList', 'nodeList'],
    data() {
        return {
            timelineList: [],
            readList: [],

            readDialog: false,
        };
    },
    methods: {
        color(params, status) {
            let color;
            switch (params) {
                // number type means node title, string type means jobs in node object
                // node did not complete
                case 0: {
                    color = 'pink';
                    break;
                }
                // node completed
                case 1: {
                    color = 'primary';
                    break;
                }
                // node countersign formFlow
                case 2: {
                    color = 'amber';
                    break;
                }
                // joint draft formFlow
                case 5: {
                    color = 'cyan';
                    break;
                }
                // common formFlow
                case '1': {
                    color = status === 1 ? 'pink' : 'primary';

                    // joint draft
                    if (status === 5) {
                        color = 'cyan';
                    }
                    break;
                }
                // countersign formFlow
                case '2': {
                    color = status === 1 ? 'amber' : 'teal';
                    break;
                }
                // joint draft formFlow
                case '5': {
                    color = status === 1 ? 'primary' : 'cyan';
                    break;
                }
                default: {
                    color = 'grey';
                    break;
                }
            }

            return color;
        },
        readListDialog(list) {
            this.readList = list;
            this.readDialog = true;
        },
    },
    mounted() {
        const nodeList = [...this.nodeList];
        nodeList.pop();
        this.timelineList = mixedNodeListAndJobList(nodeList, this.jobList);
    },
};
</script>

<style scoped></style>
