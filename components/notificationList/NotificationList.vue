<template>
    <v-card class="pa-4">
        <v-card-title>通知列表</v-card-title>
        <v-card-text>
            <v-spacer>
                <v-row>
                    <v-text-field
                        v-model="searchContent"
                        clearable
                        label="标题"
                        style="max-width: 300px"
                    ></v-text-field>
                    <v-col style="display: flex; align-items: center">
                        <v-btn color="primary" @click="searchContentList"
                            >查找
                        </v-btn>
                    </v-col>
                </v-row>
            </v-spacer>

            <div style="overflow: auto">
                <v-simple-table style="min-width: 300px">
                    <thead>
                        <tr>
                            <td>标题</td>
                            <td v-if="!$vuetify.breakpoint.xs">发布时间</td>
                            <td>操作</td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="item in notificationList" :key="item.id">
                            <td>{{ item.title }}</td>
                            <td v-if="!$vuetify.breakpoint.xs">
                                {{ item.created_at }}
                            </td>
                            <td>
                                <v-btn
                                    color="primary"
                                    outlined
                                    small
                                    @click="read(item.id)"
                                    >阅读
                                </v-btn>
                                <v-btn
                                    v-if="
                                        !$vuetify.breakpoint.xs &&
                                        isAllowedModifyNotification
                                    "
                                    color="primary"
                                    outlined
                                    small
                                    @click="modify(item.id)"
                                    >修改
                                </v-btn>
                            </td>
                        </tr>
                    </tbody>
                </v-simple-table>
            </div>

            <v-divider></v-divider>
            <v-pagination
                v-model="currentPage"
                :length="Math.ceil(this.listCount / 50)"
                :total-visible="5"
            ></v-pagination>
        </v-card-text>
    </v-card>
</template>

<script>
import { localUserRole } from '../../helper/localUserInfo';

export default {
    name: 'NotificationList',
    data() {
        return {
            searchContent: '',
            notificationList: [],

            listCount: '',
            currentPage: 1,

            isAllowedModifyNotification:
                localUserRole.includes('ROLE_CREATE_NOTIFICATION') ||
                localUserRole.includes('ROLE_ADMIN'),
        };
    },
    methods: {
        async searchContentList() {
            this.currentPage = 1;

            if (!this.searchContent) {
                await this.getNotificationList();
                return;
            }

            const result = await this.$axios.get(
                `/api/be/oa/article/list?page=0&size=99&_title=${this.searchContent}`
            );

            if (result.state === 'error') {
                alert(result.msg);
                return;
            }

            this.notificationList = result.rows;
            this.listCount = result.count;
        },

        async getNotificationList() {
            const result = await this.$axios.get(
                '/api/be/oa/article/list?page=0&size=99'
            );

            if (result.state === 'error') {
                alert(result.msg);
                return;
            }

            this.notificationList = result.rows;
            this.listCount = result.count;
        },

        read(itemId) {
            this.$router.push({
                path: '/notificationDetail',
                query: { id: itemId },
            });
        },
        modify(itemId) {
            this.$router.push({
                path: '/createNotification',
                query: { id: itemId },
            });
        },
    },
    mounted() {
        this.getNotificationList();
    },
    watch: {
        async currentPage() {
            const result = await this.$axios.get(
                `/api/be/oa/article/list?page=${this.currentPage}&size=50`
            );

            if (result.state === 'error') {
                alert(result.msg);
                return;
            }

            this.notificationList = result.rows;
            this.listCount = result.count;
        },
    },
};
</script>

<style scoped></style>
