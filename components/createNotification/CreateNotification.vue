<template>
    <v-card class="pa-4">
        <v-card-title>
            <v-btn color="primary" icon large @click="$router.go(-1)">
                <v-icon> mdi-arrow-left</v-icon>
            </v-btn>
        </v-card-title>
        <v-card-text>
            <v-form ref="notifyForm">
                <v-row>
                    <v-col>
                        <v-text-field
                            v-model="notificationTitle"
                            :rules="[rules.required]"
                            label="通知标题"
                        ></v-text-field>
                    </v-col>
                    <v-col>
                        <v-select
                            v-model="chosenType"
                            :items="notificationTypes"
                            :rules="[rules.required]"
                            label="通知类型"
                        ></v-select>
                    </v-col>
                </v-row>

                <v-row>
                    <v-col>
                        <VueEditor v-model="content" />
                    </v-col>
                </v-row>
                <v-row>
                    <v-col>
                        <FileUploadV1
                            :allowUpload="true"
                            :filesProps="filesList"
                            :getFileInfo="getFileInfo"
                        />
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>
        <v-card-actions>
            <v-row>
                <v-col>
                    <v-btn color="primary" @click="submit">保存</v-btn>
                </v-col>
            </v-row>
        </v-card-actions>
    </v-card>
</template>

<script>
import FileUploadV1 from '../FileUploadV1.vue';
import { articleGet, articleSet } from '../../helper/APIService';

export default {
    name: 'CreateNotification',
    components: { FileUploadV1 },
    data() {
        return {
            rules: {
                required: (value) => !!value || '此条目不能为空',
            },
            notificationTitle: '',
            chosenType: 1,
            content: '',
            notificationTypes: [
                { text: '通知公告', value: 1 },
                { text: '企业参考资料', value: 2 },
                { text: '流程表单下载', value: 3 },
                { text: '电话表', value: 4 },
                { text: '规章制度', value: 5 },
            ],

            filesList: '',
            folderId: '',
        };
    },
    methods: {
        getFileInfo(filesList) {
            this.filesList = filesList;
        },
        async submit() {
            if (!this.$refs.notifyForm.validate()) {
                return;
            }

            const formData = {
                title: this.notificationTitle,
                content: this.content,
                attach_files: this.filesList,
                type: this.chosenType,
                extra: JSON.stringify(this.folderId),
                status: 1,
            };

            await articleSet({
                axios: this.$axios,
                articleId: this.$route.query.id,
                articleData: formData,
            });

            await this.$router.push({ path: '/notificationList' });
        },

        async getInfo() {
            const articleData = await articleGet({
                axios: this.$axios,
                articleId: this.$route.query.id,
            });

            this.notificationTitle = articleData.title;
            this.content = articleData.content;
            this.filesList = articleData.attach_files;
            this.chosenType = articleData.type;
        },
    },
    mounted() {
        if (this.$route.query.id) {
            this.getInfo();
        }
    },
};
</script>

<style scoped></style>
