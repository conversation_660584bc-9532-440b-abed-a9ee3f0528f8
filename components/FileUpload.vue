<template>
    <div>
        <FilePond
            v-if="allowUpload"
            ref="attach_file_pond"
            :acceptedFileTypes="fileTypes"
            :files="[]"
            :server="attach_file_server"
            allowFileSizeValidation="true"
            allowFileTypeValidation="true"
            allowMultiple="true"
            allowProcess="true"
            fileValidateTypeLabelExpectedTypes="不支持的文件类型"
            labelFileTypeNotAllowed="不支持的文件类型"
            labelIdle="如需上传文件，请拖拽到此处或点击此处"
            labelMaxFileSizeExceeded="文件大小超限,最大60MB"
            maxFileSize="60MB"
            name="attach_file_pond"
        />
        <v-simple-table>
            <tbody>
                <tr
                    v-for="item of filesList"
                    :key="item.name"
                    style="cursor: pointer"
                >
                    <td>{{ item.fileName }}</td>
                    <td class="text-right">
                        <v-btn class="mr-1" icon @click="previewFile(item)">
                            <v-icon>mdi-file-find</v-icon>
                        </v-btn>
                        <!--                        <v-icon class="mr-1" middle @click="editFile(item)"-->
                        <!--                            >mdi-pencil-->
                        <!--                        </v-icon>-->
                        <v-btn class="mr-1" icon @click="downloadFile(item)">
                            <v-icon>mdi-download</v-icon>
                        </v-btn>
                        <v-btn
                            :disabled="!allowUpload"
                            class="mr-1"
                            icon
                            @click="removeFile(item)"
                        >
                            <v-icon>mdi-delete</v-icon>
                        </v-btn>
                    </td>
                </tr>
            </tbody>
        </v-simple-table>
    </div>
</template>

<script>
import VueFilePond from 'vue-filepond';
import 'filepond/dist/filepond.min.css';
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size';
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type';

import AppConfig from '../constants/AppConfig';

const FilePond = VueFilePond(
    FilePondPluginFileValidateSize,
    FilePondPluginFileValidateType
);

const tokenValue = localStorage.getItem('tokenValue');

export default {
    name: 'FileUpload',
    // props: ['filesProps', 'folderIdProps', 'getFileInfo', 'allowUpload'],
    props: {
        filesProps: {},
        folderIdProps: {},
        getFileInfo: {},
        allowUpload: {
            default: true,
        },
    },
    components: { FilePond },
    data() {
        return {
            filesList: [],
            folderId: '',
            attach_file_server: {
                process: (fieldName, file, load, progress) => {
                    const formData = new FormData();
                    formData.append('file', file, file.name);

                    if (!this.folderId) {
                        this.getTempFolderId();
                    }

                    this.$axios({
                        url: `/api/v1/files?folder_id=${this.folderId}&readonly=false`,
                        method: 'POST',
                        data: formData,
                        onUploadProgress: (progressEvent) => {
                            progress(
                                progressEvent.lengthComputable,
                                progressEvent.loaded,
                                progressEvent.total
                            );
                        },
                    })
                        .then((res) => {
                            if (res.state !== 'error') {
                                const uploadFile = res.data;

                                this.filesList.push({
                                    fileName: uploadFile.name,
                                    fileId: uploadFile.id,
                                });
                            }

                            this.getFileInfo(
                                JSON.stringify(this.filesList),
                                JSON.stringify({ folderid: this.folderId })
                            );
                        })
                        .catch((error) => {
                            console.log(error);
                        });
                },
            },
            fileTypes: [
                'application/kswps',
                'application/kset',
                'application/ksdps',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'application/vnd.ms-powerpoint',
                'application/pdf',
                'text/csv',
                'application/rar',
                'application/zip',
                'image/jpeg',
                'image/png',
                'image/jpeg',
                'image/gif',
            ],
        };
    },
    methods: {
        async getTempFolderId() {
            const result = await this.$axios.post(`/api/v1/folders`, {
                folder_name: Date.now(),
                share_mode: 'private',
            });
            this.folderId = result.data.id;
        },

        async previewFile(file) {
            const previewUrl = `${AppConfig.previewServer}/?file=${AppConfig.previewServer2}/files/${file.fileId}/origin&token=Bearer%20${tokenValue}&close=true`;
            window.open(previewUrl, '_blank');
        },

        async downloadFile(file) {
            window.location.href = `${AppConfig.previewServer2}/files/${file.fileId}/download?token=${tokenValue}`;
        },

        removeFile(file) {
            this.filesList = this.filesList.filter((item) => {
                return item.fileName !== file.fileName;
            });

            this.getFileInfo(
                JSON.stringify(this.filesList),
                JSON.stringify({ folderid: this.folderId })
            );
        },
    },

    watch: {
        folderIdProps(folderId) {
            this.folderId = JSON.parse(folderId).folderid;
        },

        filesProps(filesProps) {
            this.filesList = JSON.parse(filesProps);
        },
    },
};
</script>

<style scoped></style>
