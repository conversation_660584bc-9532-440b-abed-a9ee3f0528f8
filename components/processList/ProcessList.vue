<template>
    <v-card class="pa-4" style="overflow: auto">
        <v-card-text style="min-width: 300px">
            <v-spacer>
                <v-row>
                    <v-select
                        v-model="currentType"
                        :items="searchType"
                        label="查询方式"
                        style="max-width: 100px; margin-right: 5px"
                    ></v-select>

                    <v-text-field
                        v-model="searchContent"
                        label="查询内容"
                        style="max-width: 150px"
                    ></v-text-field>

                    <v-col style="display: flex; align-items: center">
                        <v-btn color="primary" @click="searchContentList"
                            >查询
                        </v-btn>
                    </v-col>
                </v-row>
            </v-spacer>

            <v-simple-table>
                <thead>
                    <tr>
                        <td>标题</td>
                        <td>流程名称</td>
                        <td v-if="!$vuetify.breakpoint.xs">状态</td>
                        <td v-if="!$vuetify.breakpoint.xs">当前办理人</td>
                        <td v-if="!$vuetify.breakpoint.xs">起草时间</td>
                        <td v-if="!$vuetify.breakpoint.xs">操作</td>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="item in processList" :key="item.id">
                        <td>{{ item.title }}</td>
                        <td>{{ item.Source?.name }}</td>
                        <td v-if="!$vuetify.breakpoint.xs">
                            {{ item.status === 2 ? '结束' : '进行中' }}
                        </td>
                        <td v-if="!$vuetify.breakpoint.xs">
                            {{
                                formatCurrentHandler(item?.CurrentJob) ||
                                '已完成'
                            }}
                        </td>
                        <td v-if="!$vuetify.breakpoint.xs">
                            {{ item.created_at }}
                        </td>
                        <td v-if="!$vuetify.breakpoint.xs">
                            <v-btn color="primary" @click="checkDetail(item)">
                                查看
                            </v-btn>
                        </td>
                    </tr>
                </tbody>
            </v-simple-table>

            <v-divider></v-divider>
            <v-pagination
                v-model="currentPage"
                :length="Math.ceil(this.listCount / 20)"
                :total-visible="5"
            ></v-pagination>
        </v-card-text>
    </v-card>
</template>

<script>
import AppConfig from '../../constants/AppConfig';
import currentPromoter from '../../helper/currentPromoter';

export default {
    name: 'ProcessList',
    data() {
        return {
            searchType: [
                { text: '按标题', value: 1 },
                { text: '按类型', value: 2 },
            ],

            currentType: 1,

            searchContent: '',

            processList: [],

            listCount: '',
            currentPage: 1,
        };
    },

    methods: {
        async searchContentList() {
            if (this.currentType === 1) {
                const result = await this.$axios.get(
                    `/api/be/workflow/list?page=0&size=20&_name=${this.searchContent}`
                );
                this.processList = result.rows;
            } else if (this.currentType === 2) {
                const result = await this.$axios.get(
                    `/api/be/workflow/list?page=0&size=20&_source_name=${this.searchContent}`
                );
                this.processList = result.rows;
            }
        },
        async getProcessList() {
            const result = await this.$axios.get(
                '/api/be/workflow/list?page=0&size=20'
            );

            if (result.state === 'error') {
                alert(result.msg);
                return;
            }
            console.log(result);

            this.processList = result.rows;
            this.listCount = result.count;
        },
        checkDetail(item) {
            if (item.status === 2) {
                // this.$router.push({
                //     path: `/${AppConfig.sys_code_prefix}_flow/flow_${item.sourceid}`,
                //     query: {
                //         workflowId: item.id,
                //     },
                // });
                window.open(this.$router.resolve({
                    path: `/${AppConfig.sys_code_prefix}_flow/flow_${item.sourceid}`,
                    query: {
                        workflowId: item.id,
                    },
                }).href, '_blank');
                return;
            }
            // this.$router.push({
            //     path: `/${AppConfig.sys_code_prefix}_flow/flow_${item.sourceid}`,
            //     query: {
            //         workflowId: item.id,
            //         nodeId: item.CurrentJob[0].nodeid,
            //         jobId: item.CurrentJob[0].id,
            //     },
            // });
            window.open(this.$router.resolve({
                path: `/${AppConfig.sys_code_prefix}_flow/flow_${item.sourceid}`,
                query: {
                    workflowId: item.id,
                    nodeId: item.CurrentJob[0].nodeid,
                    jobId: item.CurrentJob[0].id,
                },
            }).href, '_blank');
        },
        formatCurrentHandler(jobs) {
            return currentPromoter(jobs);
        },
    },

    mounted() {
        this.getProcessList();
    },

    watch: {
        async currentPage() {
            let result;

            if (this.currentType === 1) {
                result = await this.$axios.get(
                    `/api/be/workflow/list?page=${
                        this.currentPage - 1
                    }&size=20&_name=${this.searchContent}`
                );
            } else if (this.currentType === 2) {
                result = await this.$axios.get(
                    `/api/be/workflow/list?page=${
                        this.currentPage - 1
                    }&size=20&_source_name=${this.searchContent}`
                );
            } else {
                result = await this.$axios.get(
                    `/api/be/workflow/list?page=${this.currentPage - 1}&size=20`
                );
            }

            if (result.state === 'error') {
                alert(result.msg);
            }

            this.processList = result.rows;
            this.listCount = result.count;
        },
    },
};
</script>

<style scoped></style>
