<template>
    <v-menu bottom rounded>
        <template v-slot:activator="{ on, attrs }">
            <v-btn icon v-bind="attrs" v-on="on">
                <v-badge
                    :value="unreadJobList.length > 0 || false"
                    color="primary"
                    dot
                    left
                    offset-x="13"
                    offset-y="13"
                >
                    <v-avatar size="32">
                        <v-icon>mdi-email</v-icon>
                    </v-avatar>
                </v-badge>
            </v-btn>
        </template>
        <v-card max-width="300px">
            <v-card-text style="overflow: auto">
                <v-list dense style="max-height: 500px">
                    <v-list-item-title>待办消息</v-list-item-title>
                    <v-list-item
                        v-for="item in unreadJobList"
                        :key="item.id"
                        @click="handleClick(item)"
                        >{{ item.Workflow.title }}
                    </v-list-item>
                </v-list>
            </v-card-text>
        </v-card>
    </v-menu>
</template>
<script>
import AppConfig from '../../constants/AppConfig';

export default {
    name: 'NotificationList',
    data() {
        return {
            unreadJobList: [],
        };
    },
    methods: {
        handleClick(item) {
            const workflowId = item.Workflow.id;
            const jobId = item.id;
            const nodeId = item.nodeid;
            const sourceId = item.Workflow.sourceid;

            this.$router.push({
                path: `${AppConfig.sys_code_prefix}_flow/flow_${sourceId}`,
                query: { workflowId, jobId, nodeId },
            });
        },
        async getUnreadJobList() {
            const result = await this.$axios.get(
                '/api/be/oa/myjob/list?status=1&action_type=!3&type=1&reverse=1'
            );

            if (result.state === 'error') {
                alert(result.errorMsg);
            }

            this.unreadJobList = result.rows;
        },
    },
    mounted() {
        this.getUnreadJobList();
    },
};
</script>
<style scoped></style>
