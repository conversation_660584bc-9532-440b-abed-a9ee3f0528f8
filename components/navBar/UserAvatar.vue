<template>
    <v-menu bottom rounded>
        <template v-slot:activator="{ on, attrs }">
            <v-btn icon v-bind="attrs" v-on="on">
                <v-avatar size="32">
                    <img
                        :src="userAvatar"
                        alt="avatar"
                        style="width: 100%; height: auto"
                    />
                </v-avatar>
            </v-btn>
        </template>
        <v-card min-width="300px">
            <v-card-text style="background-color: #3c7dfa; color: white">
                <div>{{ userName }}</div>
                <div>{{ userEmail }}</div>
            </v-card-text>
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn
                    color="primary"
                    small
                    @click="$router.push('/userProfile')"
                    >个人详情
                </v-btn>
                <v-btn small @click="quitAccount">退出登录</v-btn>
            </v-card-actions>
        </v-card>
    </v-menu>
</template>

<script>
import { PolyCustomOAuthLogoutUri } from '../../constants/poly-auth';
import { localUserInfo } from '../../helper/localUserInfo';

export default {
    name: 'UserAvatar',
    data() {
        return {
            userName: '',
            userEmail: '',
            userAvatar: '',
        };
    },

    methods: {
        quitAccount() {
            localStorage.clear();

            const poly = window.open(PolyCustomOAuthLogoutUri, '_blank');

            setTimeout(() => {
                poly.close();
            }, 1000);

            this.$router.replace('/loginRedirect');
        },
    },

    mounted() {
        this.userName = localUserInfo.fullName;
        this.userEmail = localUserInfo.email;
        this.userAvatar = localUserInfo.avatar;
    },
};
</script>

<style scoped></style>
