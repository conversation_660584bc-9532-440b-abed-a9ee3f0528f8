<template>
    <div>
        <FilePond
            v-if="allowUpload"
            ref="filePond"
            :acceptedFileTypes="fileTypes"
            :fileValidateTypeDetectType="detectType"
            :files="[]"
            :files-exist-error="'文件已存在'"
            :max-file-size="60000000"
            :server="attach_file_server"
            allowFileSizeValidation="true"
            allowFileTypeValidation="true"
            allowMultiple="true"
            allowProcess="true"
            fileValidateTypeLabelExpectedTypes="不支持的文件类型或文件已存在"
            label-button-abort-item-load="取消"
            label-button-abort-item-processing="取消"
            label-button-remove-item="移除"
            label-button-undo-item-processing="撤销"
            label-file-loading="文件加载中"
            label-file-processing="上传中"
            label-file-processing-aborted="上传已取消"
            label-file-processing-revert-error="撤销失败"
            label-file-removed="文件已删除"
            label-file-waiting="请等待"
            label-tap-to-cancel="点击取消"
            label-tap-to-retry="点击重试"
            label-tap-to-undo="点击撤销"
            labelFileProcessingComplete="上传完成"
            labelFileProcessingError="上传失败"
            labelFileTypeNotAllowed="不支持的文件类型或文件已存在"
            labelIdle="如需上传文件，请拖拽到此处或点击此处"
            labelMaxFileSizeExceeded="文件大小超限,最大60MB"
            maxFileSize="60MB"
            maxParallelUploads="10"
            name="filePond"
        />
        <v-simple-table>
            <tbody>
                <tr v-for="item of filesList" :key="item.file_name">
                    <td>{{ item.origin_name }}</td>
                    <td class="text-right">
                        <v-btn class="mr-1" icon @click="previewFileV1(item)">
                            <v-icon>mdi-file-find</v-icon>
                        </v-btn>
                        <v-btn class="mr-1" icon @click="downloadFileV1(item)">
                            <v-icon>mdi-download</v-icon>
                        </v-btn>
                        <v-btn
                            v-if="allowUpload"
                            class="mr-1"
                            icon
                            @click="removeFileV1(item)"
                        >
                            <v-icon>mdi-delete</v-icon>
                        </v-btn>
                    </td>
                </tr>
            </tbody>
        </v-simple-table>
    </div>
</template>

<script>
import VueFilePond from 'vue-filepond';
import 'filepond/dist/filepond.min.css';
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size';
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type';

import AppConfig from '../constants/AppConfig';

const FilePond = VueFilePond(
    FilePondPluginFileValidateSize,
    FilePondPluginFileValidateType
);

const tokenValue = localStorage.getItem(
    `${AppConfig.sys_code_prefix}-tokenValue`
);

export default {
    name: 'FileUploadV1',
    props: {
        filesProps: {},
        getFileInfo: {},
        allowUpload: {
            default: false,
        },
    },
    components: { FilePond },
    data() {
        return {
            attach_file_server: {
                process: (
                    _filedName,
                    file,
                    _metadata,
                    load,
                    _error,
                    progress
                ) => {
                    const fileData = new FormData();
                    fileData.append('file', file, file.name);

                    this.$axios({
                        url: '/api/util/fileupload',
                        method: 'POST',
                        data: fileData,
                        onUploadProgress: (progressEvent) => {
                            const percent = progressEvent.lengthComputable
                                ? Math.round(
                                      (progressEvent.loaded * 100) /
                                          progressEvent.total
                                  )
                                : 0;
                            progress(
                                progressEvent.lengthComputable,
                                progressEvent.loaded,
                                progressEvent.total
                            );

                            this.$emit('upload-progress', percent);
                        },
                    })
                        .then((res) => {
                            if (res.state !== 'error') {
                                const uploadedFile = res[0];
                                this.filesList.push(uploadedFile);
                                this.getFileInfo(
                                    JSON.stringify(this.filesList)
                                );
                                load(`${uploadedFile.file_name} 上传成功`);
                            }
                        })
                        .catch((error) => {
                            load('上传失败');
                        });
                },
            },

            filesList: [],

            fileTypes: [
                'application/kswps',
                'application/ksdps',
                'application/kset',
                'application/msword',
                'application/octet-stream',
                'application/pdf',
                'application/rar',
                'application/vnd.ms-excel',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.rar',
                'application/x-rar',
                'application/x-rar-compressed',
                'application/x-zip-compressed',
                'application/zip',
                'image/gif',
                'image/jpeg',
                'image/png',
                'multipart/x-zip',
                'text/csv',
            ],
        };
    },
    methods: {
        initFilesList(value) {
            try {
                this.filesList = value ? JSON.parse(value) : [];
            } catch (e) {
                console.error('Invalid filesProps format:', e);
                this.filesList = [];
            }
        },
        detectType(file, type) {
            return new Promise((resolve, reject) => {
                // 添加文件名验证
                if (!file.name || typeof file.name !== 'string') {
                    reject('无效的文件名');
                    return;
                }

                if (file.size > 60 * 1024 * 1024) {
                    reject('文件大小超过60MB限制');
                    return;
                }

                const fileExists = this.filesList.some(
                    (item) => item.origin_name === file.name
                );

                if (!fileExists) {
                    // 验证文件类型是否在允许列表中
                    if (this.fileTypes.includes(type)) {
                        resolve(type);
                    } else {
                        reject('不支持的文件类型');
                    }
                } else {
                    reject('文件已存在');
                }
            });
        },
        previewFileV1(item) {
            const encodedUrl = encodeURIComponent(
                `${AppConfig.subFileServer}${item.url}`
            );
            const encodedToken = encodeURIComponent(`Bearer ${tokenValue}`);
            const previewUrl = `${AppConfig.previewServer}/?file=${encodedUrl}&token=${encodedToken}`;
            window.open(previewUrl);
        },
        async downloadFileV1(item) {
            try {
                const downloadUrl = `${AppConfig.subFileServer}${item.url}`;
                const response = await this.$axios.get(
                    `/api/util/download?filepath=${downloadUrl}&filename=${item.file_name}`,
                    {
                        responseType: 'blob',
                        headers: {
                            Authorization: `Bearer ${tokenValue}`,
                        },
                    }
                );

                const blobUrl = window.URL.createObjectURL(response);
                const downloadFile = document.createElement('a');
                downloadFile.href = blobUrl;
                downloadFile.download = item.origin_name;
                document.body.appendChild(downloadFile);
                downloadFile.click();
                document.body.removeChild(downloadFile);
                URL.revokeObjectURL(blobUrl);
            } catch (error) {
                console.error('Download failed:', error);
                this.$emit('download-error', error);
                this.$emit('show-error', '文件下载失败');
            }
        },
        removeFileV1(file) {
            this.filesList = this.filesList.filter((item) => {
                return item.file_name !== file.file_name;
            });

            this.getFileInfo(JSON.stringify(this.filesList));
        },
    },

    created() {
        this.initFilesList(this.filesProps);
    },
    watch: {
        filesProps: {
            handler(newValue) {
                this.initFilesList(newValue);
            },
            immediate: true,
        },
    },
    beforeDestroy() {
        if (this.$refs.filePond) {
            this.$refs.filePond.removeFiles();
        }

        this.filesList = [];
    },
};
</script>
