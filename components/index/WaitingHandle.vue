<template>
    <v-card
        class="rounded-lg pa-4"
        elevation="6"
        style="min-height: 300px; height: 100%"
    >
        <v-card-title>待我处理</v-card-title>
        <v-card-text style="overflow: auto">
            <v-spacer style="position: absolute; top: 6px; right: 6px">
                <v-btn icon>
                    <v-icon>mdi-dots-horizontal-circle-outline</v-icon>
                </v-btn>
            </v-spacer>

            <v-chip-group active-class="primary--text" column mandatory>
                <v-chip
                    v-for="tag in toDoTags"
                    :key="tag.type"
                    small
                    @click="changeTags(tag.type)"
                    >{{ tag.label }}
                </v-chip>
            </v-chip-group>

            <v-list dense style="max-height: 300px">
                <v-list-item
                    v-for="item of toDoJobs"
                    :key="item.id"
                    @click="linkToForm(item)"
                >
                    {{ item.Workflow?.title || item.title }}
                </v-list-item>
            </v-list>
        </v-card-text>
    </v-card>
</template>
<script>
import AppConfig from '../../constants/AppConfig';

export default {
    name: 'WaitingHandle',
    data() {
        return {
            toDoJobs: [],
            toDoTags: [
                { type: '-1', label: '全部' },
                { type: '1', label: '呈批件' },
                { type: '2', label: '阅件' },
                { type: '!1,2', label: '其他' },
                { type: '3rd_system', label: '子系统' },
            ],
        };
    },
    methods: {
        linkToForm(item) {
            if (!!item.Workflow){
                const workflowId = item.Workflow.id;
                const jobId = item.id;
                const nodeId = item.nodeid;
                const sourceId = item.Workflow.sourceid;

                this.$router.push({
                    path: `${AppConfig.sys_code_prefix}_flow/flow_${sourceId}`,
                    query: { workflowId, jobId, nodeId },
                });
            }
            if(!!item.from_system_code && !!item.target_url){
				let uri = item.target_url;
				console.log('三方待办url',uri)
				window.open(uri);
			}
        },

        async changeTags(type) {
            if (type === '3rd_system') {
                const result = await this.$axios.get(
                    '/api/be/oa/myjob/indexlist4hq?page=0&size=8&status=1&action_type=!3&reverse=1&business_type=3rd_system_todo'
                );
                this.toDoJobs = result.rows;
            } else if (type === '-1') {
                const result = await this.$axios.get(
                    '/api/be/oa/myjob/indexlist4hq?page=0&size=8&status=1&action_type=!3&reverse=1'
                );
                this.toDoJobs = result.rows;
            } else {
                const result = await this.$axios.get(
                    `/api/be/oa/myjob/indexlist?page=0&size=8&reverse=1&status=1&action_type=!3&type=${type}`
                );
                this.toDoJobs = result.rows;
            }
        },
        async getTodoJobs() {
            const result = await this.$axios.get(
                '/api/be/oa/myjob/indexlist4hq?page=0&size=8&status=1&action_type=!3&reverse=1'
            );
            this.toDoJobs = result.rows;
        },
    },
    mounted() {
        this.getTodoJobs();
    },
};
</script>
