<template>
    <v-card
        class="rounded-lg pa-4"
        elevation="6"
        style="min-height: 300px; height: 100%"
    >
        <v-card-title>通知公告</v-card-title>
        <v-card-text style="overflow: auto">
            <v-spacer style="position: absolute; top: 6px; right: 6px">
                <v-btn icon>
                    <v-icon>mdi-dots-horizontal-circle-outline</v-icon>
                </v-btn>
            </v-spacer>

            <v-list dense flat style="max-height: 300px">
                <v-list-item-group color="primary">
                    <v-list-item
                        v-for="item of announcement"
                        :key="item.id"
                        @click="showDetail(item.id)"
                    >
                        <v-icon>mdi-mail</v-icon>
                        <v-list-item-title>{{ item.title }}</v-list-item-title>
                    </v-list-item>
                </v-list-item-group>
            </v-list>
        </v-card-text>
    </v-card>
</template>

<script>
import { NoticeListGet } from '../../helper/APIService';

export default {
    name: 'NoticeAnnouncement',
    data() {
        return {
            announcement: [],
        };
    },
    methods: {
        showDetail(itemId) {
            this.$router.push({
                path: '/notificationDetail',
                query: { id: itemId },
            });
        },
        async getAnnouncement() {
            const { noticeList } = await NoticeListGet(this.$axios);
            this.announcement = noticeList;
        },
    },
    mounted() {
        this.getAnnouncement();
    },
};
</script>

<style scoped></style>
