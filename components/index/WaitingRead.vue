<template>
    <v-card
        class="rounded-lg pa-4"
        elevation="6"
        style="min-height: 300px; height: 100%"
    >
        <v-card-title>待我查阅</v-card-title>
        <v-card-text style="overflow: auto">
            <v-spacer style="position: absolute; top: 6px; right: 6px">
                <v-btn icon>
                    <v-icon>mdi-dots-horizontal-circle-outline</v-icon>
                </v-btn>
            </v-spacer>

            <v-chip-group active-class="primary--text" column mandatory>
                <v-chip
                    v-for="tag in tags"
                    :key="tag.type"
                    small
                    @click="changeTags(tag.type)"
                    >{{ tag.label }}
                </v-chip>
            </v-chip-group>

            <v-list dense flat style="max-height: 300px">
                <v-list-item-group color="primary">
                    <v-list-item v-for="item of copyJobs" :key="item.id">
                        <v-list-item-icon>
                            <v-icon>mdi-mail</v-icon>
                        </v-list-item-icon>
                        <v-list-item-content @click="handleClick(item)">
                            <v-list-item-title
                                >{{ item.Workflow.title }}
                            </v-list-item-title>
                        </v-list-item-content>
                    </v-list-item>
                </v-list-item-group>
            </v-list>
        </v-card-text>
    </v-card>
</template>

<script>
import AppConfig from '../../constants/AppConfig';

export default {
    name: 'WaitingRead',
    data() {
        return {
            tags: [
                { type: '-1', label: '全部' },
                { type: '1', label: '呈批件' },
                { type: '2', label: '阅件' },
                { type: '!1,2', label: '其他' },
            ],
            copyJobs: [],
        };
    },
    methods: {
        handleClick(item) {
            const workflowId = item.Workflow.id;
            const jobId = item.id;
            const nodeId = item.nodeid;
            const sourceId = item.Workflow.sourceid;

            this.$router.push({
                path: `${AppConfig.sys_code_prefix}_flow/flow_${sourceId}`,
                query: { workflowId, jobId, nodeId },
            });
        },
        async changeTags(type) {
            if (type === '-1') {
                const result = await this.$axios.get(
                    '/api/be/oa/myjob/unread?page=0&size=8'
                );
                this.copyJobs = result.rows;
            } else {
                const result = await this.$axios.get(
                    `/api/be/oa/myjob/unread?page=0&size=8&type=${type}`
                );
                this.copyJobs = result.rows;
            }
        },
        async getCopyJobs() {
            const result = await this.$axios.get(
                '/api/be/oa/myjob/unread?page=0&size=8'
            );
            this.copyJobs = result.rows;
        },
    },
    mounted() {
        this.getCopyJobs();
    },
};
</script>

<style scoped></style>
