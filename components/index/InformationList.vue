<template>
    <v-card
        class="rounded-lg"
        elevation="6"
        style="min-height: 300px; height: 100%"
    >
        <v-card-text>
            <v-tabs v-model="currentTab" show-arrows>
                <v-tab align-with-title center-active>企业参考资料</v-tab>
                <v-tab align-with-title center-active>流程表单下载</v-tab>
                <v-tab align-with-title center-active>企业电话表</v-tab>
                <v-tab align-with-title center-active>企业规章制度</v-tab>
            </v-tabs>

            <v-tabs-items v-model="currentTab">
                <v-tab-item transition="fade-transition">
                    <v-list dense flat>
                        <v-list-item-group color="primary">
                            <v-list-item
                                v-for="item of references.slice(0, 8)"
                                :key="item.id"
                                @click="showDetail(item.id)"
                            >
                                <v-icon>mdi-mail</v-icon>
                                <v-list-item-title
                                    >{{ item.title }}
                                </v-list-item-title>
                            </v-list-item>
                        </v-list-item-group>
                    </v-list>
                </v-tab-item>
                <v-tab-item transition="fade-transition">
                    <v-list dense flat>
                        <v-list-item-group color="primary">
                            <v-list-item
                                v-for="item of flowForm.slice(0, 8)"
                                :key="item.id"
                                @click="showDetail(item.id)"
                            >
                                <v-icon>mdi-mail</v-icon>
                                <v-list-item-title
                                    >{{ item.title }}
                                </v-list-item-title>
                            </v-list-item>
                        </v-list-item-group>
                    </v-list>
                </v-tab-item>
                <v-tab-item transition="fade-transition">
                    <v-list dense flat>
                        <v-list-item-group color="primary">
                            <v-list-item
                                v-for="item of companyPhoList.slice(0, 8)"
                                :key="item.id"
                                @click="showDetail(item.id)"
                            >
                                <v-icon>mdi-mail</v-icon>
                                <v-list-item-title
                                    >{{ item.title }}
                                </v-list-item-title>
                            </v-list-item>
                        </v-list-item-group>
                    </v-list>
                </v-tab-item>
                <v-tab-item transition="fade-transition">
                    <v-list dense flat>
                        <v-list-item-group color="primary">
                            <v-list-item
                                v-for="item of companyRuleList.slice(0, 8)"
                                :key="item.id"
                                @click="showDetail(item.id)"
                            >
                                <v-icon>mdi-mail</v-icon>
                                <v-list-item-title
                                    >{{ item.title }}
                                </v-list-item-title>
                            </v-list-item>
                        </v-list-item-group>
                    </v-list>
                </v-tab-item>
            </v-tabs-items>
        </v-card-text>
    </v-card>
</template>

<script>
import { infoListGet } from '../../helper/APIService';

export default {
    name: 'InformationList',
    data() {
        return {
            infoList: [],
            currentTab: '',
            references: [],
            flowForm: [],
            companyPhoList: [],
            companyRuleList: [],
        };
    },
    methods: {
        showDetail(itemId) {
            this.$router.push({
                path: '/notificationDetail',
                query: { id: itemId },
            });
        },
        async getInfoList() {
            const { infoList } = await infoListGet(this.$axios);

            this.infoList = infoList;
            this.infoList.forEach((item) => {
                switch (item.type) {
                    case 2:
                        this.references.push(item);
                        break;
                    case 3:
                        this.flowForm.push(item);
                        break;
                    case 4:
                        this.companyPhoList.push(item);
                        break;
                    case 5:
                        this.companyRuleList.push(item);
                        break;
                    default:
                        break;
                }
            });
        },
    },
    mounted() {
        this.getInfoList();
    },
};
</script>

<style scoped></style>
