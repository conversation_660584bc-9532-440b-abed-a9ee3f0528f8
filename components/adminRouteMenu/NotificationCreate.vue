<template>
    <v-list-item v-if="isAllowCreate" to="/createNotification">
        <v-list-item-title> 新建通知</v-list-item-title>
    </v-list-item>
</template>

<script>
import { localUserRole } from '../../helper/localUserInfo';

export default {
    name: 'NotificationCreate',
    data() {
        return {
            isAllowCreate:
                localUserRole.includes('ROLE_CREATE_NOTIFICATION') ||
                localUserRole.includes('ROLE_ADMIN'),
        };
    },
};
</script>

<style scoped></style>
