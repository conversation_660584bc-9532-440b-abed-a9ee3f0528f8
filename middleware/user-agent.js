import { getCustomOAuthRedirectUri } from '../constants/poly-auth';

import AppConfig from '../constants/AppConfig';

const tokenValue = localStorage.getItem(
    `${AppConfig.sys_code_prefix}-tokenValue`
);

export default async function ({ route }) {
    if (
        !tokenValue &&
        !['/login', '/login/', '/loginRedirect'].includes(route.path)
    ) {
        window.location.href = getCustomOAuthRedirectUri();
    }
}
