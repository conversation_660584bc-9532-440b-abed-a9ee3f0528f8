# 子公司配置过程

如果目录中没有下面的文件 , 需要手动创建

## 在 `constants/AppConfig.js` 文件中配置子公司 `sys_code_prefix` ,

`sys_code_prefix` 与 `pages` 中的页面相对应 , 需要正确填写

```js
// constants/AppConfig.js
export default {
    appLogo2: '/oa_logo.png',
    previewServer: 'https://poly-file.cella.fun',
    oaFileServer: '/assets/be/oa/',
    subFileServer: 'https://devoafile.poly.com.cn',
    sys_code_prefix: '',
    file_system_v2_active: true,
};

```

* 新大厦子公司为 : `OA_PLAZA`
* 文化子公司为 : `OA_CULTURE`
* 投资子公司为 : `OA_TOUZI`

## 在 `constants/poly-auth.js` 文件中配置子公司 `PolyCustomClientId`

```js
// constants/poly-auth.js
export const UserApiUrl = '//uc.poly.com.cn';

export const getCallBackUrl = () => {
    return `${window.location.protocol}//${window.location.host}/login`;
};

const PolyCustomOAuthUrl = `${UserApiUrl}/sso/polyOAuth/authorize`;

export const PolyCustomClientId = '';

export const PolyCustomOAuthLogoutUri = `${UserApiUrl}/sso/polyOAuth/logout?client_id=${PolyCustomClientId}`;

export const getCustomOAuthRedirectUri = () =>
    `${PolyCustomOAuthUrl}?response_type=code&client_id=${PolyCustomClientId}&redirect_uri=${getCallBackUrl()}&scope=openid`;

```

* 新大厦子公司为 : `wmtayAwn6mu0llSwT66T`
* 文化子公司为 : `oAppWsh5AThWthWYt5Tu`
* 投资子公司为 : `u44p5EEl460YhLTWtpEs`

## 在 `nuxt.config.js` 文件中配置子公司代理地址

在 `nuxt.config.js` 的 `proxy` 配置项中配置 `target` 属性

```js
// nuxt.config.js
import colors from 'vuetify/es5/util/colors';

export default {
    ssr: false,

    router: {
        middleware: ['user-agent'],
    },

    head: {
        titleTemplate: '%s',
        title: 'OA协同办公平台',
        htmlAttrs: {
            lang: 'zh',
        },
        meta: [
            {charset: 'utf-8'},
            {
                name: 'viewport',
                content: 'width=device-width, initial-scale=1',
            },
            {hid: 'description', name: 'description', content: ''},
            {name: 'format-detection', content: 'telephone=no'},
        ],
        link: [{rel: 'icon', type: 'image/x-icon', href: '/oa_logo.png'}],
    },

    css: [],

    plugins: ['@/plugins/axios'],

    components: true,

    buildModules: [
        '@nuxt/typescript-build',
        '@nuxtjs/vuetify',
        '@nuxtjs/google-fonts',
    ],

    modules: ['@nuxtjs/axios', 'vue2-editor/nuxt', '@nuxtjs/google-fonts'],

    axios: {
        proxy: true,
    },
    proxy: {
        '/api': {
            target: '',
            changeOrigin: true,
        },
        '/auth': {
            target: '',
            changeOrigin: true,
        },
        '/assets/be/oa': {
            target: '',
            changeOrigin: true,
        },
        '/2021': {
            target: 'http://oafile.poly.com.cn',
            changeOrigin: true,
        },
    },

    vuetify: {
        customVariables: ['~/assets/variables.scss'],
        theme: {
            dark: false,
            themes: {
                dark: {
                    primary: colors.blue.darken2,
                    accent: colors.grey.darken3,
                    secondary: colors.amber.darken3,
                    info: colors.teal.lighten1,
                    warning: colors.amber.base,
                    error: colors.deepOrange.accent4,
                    success: colors.green.accent3,
                },
            },
        },
    },

    googleFonts: {
        families: {
            Roboto: [100, 300, 400, 500, 700, 900],
        },
    },

    build: {},
};

```

* 新大厦子公司为 : <https://npoa.poly.com.cn/>
* 文化子公司为 : <https://pcoa.poly.com.cn>
* 投资子公司为 : <https://pioa.poly.com.cn/>
