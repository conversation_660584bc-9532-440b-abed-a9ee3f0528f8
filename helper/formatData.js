export const addDEPTMANAGERToList = (userList) => {
    return userList.filter(
        (item) => item.role && item.role.includes('ROLE_DEPT_MANAGER')
    );
};

export const formatDepList = (userList, depList) => {
    const mixedUserAndDepList = depList.map((item) => {
        return {
            id: item.id,
            fullName: item.name,
            children: item.children,
        };
    });

    const leaderItems = [];
    const managerItems = [
        {
            children: addDEPTMANAGERToList(userList),
            id: 0,
            fullName: '部门负责人',
        },
    ];
    const otherItems = [];

    mixedUserAndDepList.forEach((item) => {
        const name = item.fullName;
        if (name.includes('公司领导')) {
            leaderItems.push(item);
        } else {
            otherItems.push(item);
        }
    });

    const collator = new Intl.Collator('zh-CN');
    leaderItems.sort((a, b) => collator.compare(a.fullName, b.fullName));

    return {
        mixedUserAndDepList: [...leaderItems, ...managerItems, ...otherItems],
    };
};

export const mixedNodeListAndJobList = (nodeList, jobList) => {
    const mixedList = nodeList.map((node) => ({
        id: node.id,
        user: node.User.fullName,
        title: node.title,
        status: node.status,
        time: node.updated_at,
        jobList: [],
        readList: [],
    }));

    jobList?.forEach((job) => {
        const nodeIndex = mixedList.findIndex((node) => node.id === job.nodeid);
        if (nodeIndex !== -1) {
            if (job.action_type === '3') {
                mixedList[nodeIndex].readList.push(job);
            } else {
                mixedList[nodeIndex].jobList.push(job);
                mixedList[nodeIndex].jobList.sort(
                    (a, b) => new Date(a.finish_time) - new Date(b.finish_time)
                );
            }
        }
    });

    return mixedList;
};
