export const workflowCreate = async ({ axios, sourceId, workflowTitle }) => {
    const createWorkflow = await axios.post('/api/be/workflow/create', {
        title: workflowTitle,
        sourceid: sourceId,
        type: 1,
    });
    if (createWorkflow.state === 'error') {
        alert(createWorkflow.errorMsg);
        throw createWorkflow.errorMsg;
    }

    const {
        workflow: createdWorkflow,
        current_node: createdNode,
        current_job: createdJob,
    } = createWorkflow;

    const jobId = createdJob.id;
    const nodeId = createdNode.id;
    const sourceNodeId = createdNode.sourcenodeid;
    const workflowId = createdWorkflow.id;

    return { jobId, nodeId, sourceNodeId, workflowId };
};

export const workflowModify = async ({ axios, workflowId, workflowTitle }) => {
    const modifyWorkflow = await axios.post('/api/be/workflow/modify', {
        id: workflowId,
        title: workflowTitle,
    });
    if (modifyWorkflow.state === 'error') {
        alert(modifyWorkflow.errorMsg);
        throw modifyWorkflow.errorMsg;
    }
};

export const formCreate = async ({ axios, workflowId, formData }) => {
    const createFrom = await axios.post('/api/be/workflow/form/create', {
        workflowid: workflowId,
        extra: JSON.stringify(formData),
        main_files: formData.mainFiles,
        attach_files: formData.attachFiles,
        formtemplateid: 1,
    });
    if (createFrom.state === 'error') {
        alert(createFrom.errorMsg);
        throw createFrom.errorMsg;
    }

    return { formId: createFrom.id };
};

export const formModify = async ({ axios, formData, formId }) => {
    const modifyForm = await axios.post('/api/be/workflow/form/modify', {
        id: formId,
        extra: JSON.stringify(formData),
        main_files: formData.mainFiles,
        attach_files: formData.attachFiles,
    });
    if (modifyForm.state === 'error') {
        alert(modifyForm.errorMsg);
        throw modifyForm.errorMsg;
    }
};

export const getNodeBySourceNodeId = async ({ axios, sourceNodeId }) => {
    const nodeList = await axios.get(
        `/api/be/workflow/source/node/getchildren?sourcenodeid=${sourceNodeId}`
    );
    if (nodeList.state === 'error') {
        alert(nodeList.errorMsg);
        throw nodeList.errorMsg;
    }

    return { nodeList };
};

export const getUserByNodeRole = async ({ axios, role }) => {
    const userList = await axios.get(
        `/api/be/oa/user/list?page=0&size=999&role=${role}`
    );
    if (userList.state === 'error') {
        alert(userList.errorMsg);
        throw userList.errorMsg;
    }

    return { userList: userList.rows };
};

export const nodeAppend = async ({
    axios,
    formId,
    nodeId,
    selectedNodeName,
    sourceNodeId,
    userId,
    workflowId,
}) => {
    const appendNode = await axios.post('/api/be/workflow/node/append', {
        formid: formId,
        prev_id: nodeId,
        sourcenodeid: sourceNodeId,
        title: selectedNodeName,
        uid: userId,
        workflowid: workflowId,
    });
    if (appendNode.state === 'error') {
        alert(appendNode.errorMsg);
        throw appendNode.errorMsg;
    }
};

export const nodeApprove = async ({ axios, nodeId, is_end }) => {
    const endNode = await axios.post('/api/be/workflow/node/approve', {
        id: nodeId,
        is_end: !!is_end,
    });
    if (endNode.state === 'error') {
        alert(endNode.errorMsg);
        throw endNode.errorMsg;
    }
};

export const getJointUserByCompanyId = async ({ axios, companyId }) => {
    const jointDraftingUserList = await axios.get(
        `/api/be/oa/ucuser/list?companyId=${companyId}`
    );
    if (jointDraftingUserList.state === 'error') {
        alert(jointDraftingUserList.errorMsg);
        throw jointDraftingUserList.errorMsg;
    }

    return { userList: jointDraftingUserList.data };
};

export const jointDraftingCreate = async ({
    axios,
    workflowId,
    jobId,
    nodeId,
    userId,
}) => {
    const createJointDrafting = await axios.post('/api/be/oa/job/jdt/create', {
        workflowid: workflowId,
        jobid: jobId,
        nodeid: nodeId,
        uids: userId,
    });
    if (createJointDrafting.state === 'error') {
        alert(createJointDrafting.errorMsg);
        throw createJointDrafting.errorMsg;
    }
};

export const countersignCreate = async ({
    axios,
    jobId,
    nodeId,
    uids,
    userComment,
    workflowId,
}) => {
    const createCountersign = await axios.post(
        '/api/be/oa/job/countersign/create',
        {
            jobid: jobId,
            nodeid: nodeId,
            uids,
            user_result: userComment,
            workflowid: workflowId,
        }
    );
    if (createCountersign.state === 'error') {
        alert(createCountersign.errorMsg);
        throw createCountersign.errorMsg;
    }
};

export const getAllUser = async (axios) => {
    const userList = await axios.get('/api/be/oa/user/list?page=0&size=999');
    if (userList.state === 'error') {
        alert(userList.errorMsg);
        throw userList.errorMsg;
    }

    return { userList: userList.rows };
};

export const getAllDepartment = async ({ axios, companyId }) => {
    const departmentList = await axios.get(
        `/api/be/oa/dept/list?deptid=${companyId}`
    );
    if (departmentList.state === 'error') {
        alert(departmentList.errorMsg);
        throw departmentList.errorMsg;
    }

    return {
        departmentList: departmentList.data
            .map((item) => {
                if (item.name.includes('段赵清')) {
                    item.children.push({
                        id: 'd604ff92-48be-11eb-8f84-7b9620caa627',
                        fullName: '段赵清',
                        nickname: null,
                        username: 'duanzhaoqing',
                        gender: null,
                        mobile: '13691510641',
                        avatar: 'https://poly-hr.s3.cn-northwest-1.amazonaws.com.cn/6b0fce078b734bbcb2029916a723b9b8.jpg',
                        companyid: 'efbe9325-f85f-4562-b540-24dcb91317aa',
                        departmentid: 'cd01f5af-34a0-4e2d-9974-ccbe018b81c5',
                        role: 'ROLE_CHAIRMAN,ROLE_TODO_SMS',
                    });
                }
                return item;
            })
            .filter((item) => item.children.length > 0),
    };
};

export const workflowDelete = async ({ axios, workflowId }) => {
    const deleteWorkflow = await axios.post('/api/be/workflow/delete', {
        id: workflowId,
    });
    if (deleteWorkflow.state === 'error') {
        alert(deleteWorkflow.errorMsg);
        throw deleteWorkflow.errorMsg;
    }
};

export const readCreate = async ({
    axios,
    nodeId,
    uids,
    userComment,
    workflowId,
}) => {
    const createRead = await axios.post('/api/be/oa/job/read/create', {
        nodeid: nodeId,
        uids,
        user_result: userComment,
        workflowid: workflowId,
    });
    if (createRead.state === 'error') {
        alert(createRead.errorMsg);
        throw createRead.errorMsg;
    }
};

export const workflowReset = async ({ axios, workflowId }) => {
    const resetWorkflow = await axios.post('/api/be/workflow/node/reset', {
        workflowid: workflowId,
    });
    if (resetWorkflow.state === 'error') {
        alert(resetWorkflow.errorMsg);
        throw resetWorkflow.errorMsg;
    }
};

export const chipsGet = async (axios) => {
    const chips = await axios.get('/api/be/oa/job/getChipsAndContact');
    if (chips.state === 'error') {
        alert(chips.errorMsg);
        throw chips.errorMsg;
    }
    // if (chips.user_chips === '') return null;

    return {
        chips: chips.user_chips === '' ? [] : chips.user_chips.split(','),
    };
};

export const chipsSet = async ({ axios, chips }) => {
    const changeChips = await axios.post('/api/be/oa/job/setchips', {
        chips,
    });
    if (changeChips.state === 'error') {
        alert(changeChips.errorMsg);
        throw changeChips.errorMsg;
    }
};

export const jobModify = async ({ axios, jobId, userComment }) => {
    const modifyJob = await axios.post('/api/be/oa/job/modify', {
        id: jobId,
        result: userComment,
    });
    if (modifyJob.state === 'error') {
        alert(modifyJob.errorMsg);
        throw modifyJob.errorMsg;
    }
};

export const countersignEnd = async ({ axios, jobId, userComment }) => {
    const endCountersign = await axios.post(
        '/api/be/oa/job/countersign/approve',
        {
            id: jobId,
            result: userComment,
        }
    );
    if (endCountersign.state === 'error') {
        alert(endCountersign.errorMsg);
        throw endCountersign.errorMsg;
    }
};

export const articleSet = async ({ axios, articleId, articleData }) => {
    if (articleId) {
        const modifyArticle = await axios.post(`/api/be/oa/article/modify`, {
            ...articleData,
            id: articleId,
        });
        if (modifyArticle.state === 'error') {
            alert(modifyArticle.errorMsg);
            throw modifyArticle.errorMsg;
        }
    } else {
        const createArticle = await axios.post('/api/be/oa/article/create', {
            ...articleData,
        });
        if (createArticle.state === 'error') {
            alert(createArticle.errorMsg);
            throw createArticle.errorMsg;
        }
    }
};

export const articleGet = async ({ axios, articleId }) => {
    const getArticle = await axios.get(
        `/api/be/oa/article/info?id=${articleId}`
    );
    if (getArticle.state === 'error') {
        alert(getArticle.errorMsg);
        throw getArticle.errorMsg;
    }

    return getArticle;
};

export const infoListGet = async (axios) => {
    const infoList = await axios.get(
        '/api/be/oa/article/list?page=0&size=100&type=2,3,4,5'
    );
    if (infoList.state === 'error') {
        alert(infoList.errorMsg);
        throw infoList.errorMsg;
    }
    return { infoList: infoList.rows };
};

export const NoticeListGet = async (axios) => {
    const noticeList = await axios.get(
        '/api/be/oa/article/list?page=0&size=8&type=1'
    );
    if (noticeList.state === 'error') {
        alert(noticeList.errorMsg);
        throw noticeList.errorMsg;
    }
    return { noticeList: noticeList.rows };
};
