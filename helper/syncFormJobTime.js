export default async function syncFormJobTime({
    axios,
    currentComment,
    currentCommentField,
    formData,
    formId,
    jobId,
}) {
    const jobInfo = await axios.get(`/api/be/oa/job/info?id=${jobId}`);
    if (jobInfo.state === 'error') {
        alert(jobInfo.errorMsg);
        throw jobInfo.errorMsg;
    }

    if (jobInfo.finish_time) {
        formData[
            currentCommentField
        ][0].content = `${currentComment} ${jobInfo.finish_time}`;
    }

    const formModify = await axios.post('/api/be/workflow/form/modify', {
        id: formId,
        extra: JSON.stringify(formData),
        main_files: formData.mainFiles,
        attach_files: formData.attachFiles,
    });
    if (formModify.state === 'error') {
        alert(formModify.errorMsg);
        throw formModify.errorMsg;
    }
}
