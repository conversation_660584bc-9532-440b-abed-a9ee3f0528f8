export const adminGetAllNode = async ({ axios, sourceId }) => {
    const allNodeList = await axios.get(
        `/api/be/workflow/source/node/list?sourceid=${sourceId}`
    );
    if (allNodeList.state === 'error') {
        alert(allNodeList.errorMsg);
        return null;
    }

    return { nodeList: allNodeList };
};

export const adminNodeAppend = async ({
    axios,
    formId,
    nodeId,
    selectedNodeName,
    sourceNodeId,
    userId,
    workflowId,
}) => {
    const adminAppendNode = await axios.post(
        '/api/be/workflow/node/adminappend',
        {
            formid: formId,
            prev_id: nodeId,
            sourcenodeid: sourceNodeId,
            title: selectedNodeName,
            uid: userId,
            workflowid: workflowId,
        }
    );
    if (adminAppendNode.state === 'error') {
        alert(adminAppendNode.errorMsg);
        return;
    }
};

export const adminNodeApprove = async ({ axios, nodeId }) => {
    const adminApproveNode = await axios.post(
        '/api/be/workflow/node/adminapprove',
        {
            id: nodeId,
        }
    );
    if (adminApproveNode.state === 'error') {
        alert(adminApproveNode.errorMsg);
        return;
    }
};
