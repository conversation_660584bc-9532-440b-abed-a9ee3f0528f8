import { localUserRole } from './localUserInfo';

export default async function initForm(axios, jobId, nodeId, workflowId) {
    const [formResult, jobListResult, jobResult, nodeResult, nodeListResult] =
        await Promise.all([
            axios.get(`/api/be/workflow/form/info?workflowid=${workflowId}`),
            axios.get(
                `/api/be/oa/job/list?workflowid=${workflowId}&page=0&size=1000`
            ),
            jobId ? axios.get(`/api/be/oa/job/info?id=${jobId}`) : '',
            nodeId ? axios.get(`/api/be/workflow/node/info?id=${nodeId}`) : '',
            axios.get(`/api/be/workflow/node/list?workflowid=${workflowId}`),
        ]);

    if (
        [formResult, jobListResult, jobResult, nodeResult, nodeListResult].some(
            (result) => result.state === 'error'
        )
    ) {
        const errorMsg = [
            formResult,
            jobResult,
            nodeResult,
            nodeListResult,
        ].map((result) => result?.errorMsg);
        alert(errorMsg);
        return null;
    }

    const nodeTitle = nodeResult?.title;
    const nodeList = nodeListResult?.rows;

    const sourceNodeId = nodeResult?.sourcenodeid;
    const {
        modify_files: fileModify,
        node_right_countersign: countersignCreate,
        node_right_transfer: transferCreate,
        node_right_return: returnCreate,
        node_right_new_workflow: workflowCreate,
        uid_is_creator: uidIsCreator,
    } = nodeResult?.SourceNode ?? {};

    const formData = JSON.parse(formResult.form.extra);
    const {
        id: formId,
        doc_no: docNumber,
        doc_type: docType,
        creatorid: formCreatorId,
        created_at: formCreatedAt,
    } = formResult.form;
    const sourceId = nodeResult?.Workflow?.sourceid;

    const { action_type: actionType } = jobResult;
    const jobList = jobListResult?.rows;
    const isEnding = [
        '文印员',
        '文印员归档',
        '归档',
        '签章人归档',
        '党群工作部归档',
    ].includes(nodeTitle);
    const isStaging = jobResult.extra === 'staging';
    const isCreating = nodeTitle === '发起';
    const isReviewing =
        !!nodeTitle && nodeTitle !== '发起' && actionType === '1';
    const isSigning = actionType === '2';
    const isJointDrafting = nodeTitle === '发起' && actionType === '5';

    const isClerkSubmitAndEnd = localUserRole.includes('ROLE_PRINTER_EDITOR');

    return {
        jobInfo: {
            actionType,
            isCreating,
            isEnding: isClerkSubmitAndEnd ? false : isEnding,
            isJointDrafting,
            isReviewing,
            isSigning,
            isStaging,
            jobId,
            jobList,
        },
        formInfo: {
            docNumber,
            docType,
            formCreatedAt,
            formCreatorId,
            formData,
            formId,
        },
        nodeInfo: {
            countersignCreate,
            fileModify,
            nodeId,
            nodeList,
            nodeTitle,
            returnCreate,
            sourceId,
            sourceNodeId,
            transferCreate,
            uidIsCreator,
            workflowCreate,
        },
    };
}
