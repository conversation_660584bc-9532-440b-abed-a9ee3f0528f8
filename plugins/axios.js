/* eslint-disable no-param-reassign */
import AppConfig from '../constants/AppConfig';

export default function ({ $axios }) {
    $axios.defaults.timeout = 30000;

    $axios.interceptors.request.use(
        (config) => {
            config.headers = {
                Accept: 'application/json',
                Authorization: `Bearer ${localStorage.getItem(
                    `${AppConfig.sys_code_prefix}-tokenValue`
                )}`,
            };
            return config;
        },
        (error) => {
            console.error('request Error message', error);
            return Promise.reject(error);
        }
    );

    $axios.interceptors.response.use(
        (response) => {
            if (response.data && response.data?.errCode === '200002') {
                localStorage.clear();
            }
            return response.data;
        },
        (error) => {
            console.error(`axios error 异常：${error}`);
            if (error && error.response) {
                switch (error.response.status) {
                    case 401:
                        break;
                    case 500:
                        console.log({
                            mes: '服务器错误，请联系管理员',
                            timeout: 1500,
                            icon: 'error',
                        });
                        break;
                    case 502:
                        console.log({
                            mes: '接口502错误，请联系管理员',
                            timeout: 1500,
                            icon: 'error',
                        });
                        break;
                    case 404:
                        console.log({
                            mes: '接口404错误，请联系管理员',
                            timeout: 1500,
                            icon: 'error',
                        });
                        break;
                    default:
                        console.log({
                            mes: '服务器异常，请联系管理员',
                            timeout: 1500,
                            icon: 'error',
                        });
                }
            } else {
                console.log({
                    mes: '超时了，请稍后再试',
                    timeout: 1500,
                    icon: 'error',
                });
            }
            return Promise.reject(error); // 返回接口返回的错误信息
        }
    );
}
