{"name": "polysuboa", "version": "1.0.0", "private": true, "scripts": {"dev": "nuxt", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate"}, "dependencies": {"@nuxtjs/axios": "^5.13.6", "@nuxtjs/google-fonts": "^3.0.0-0", "bpmn-js": "^9.4.1", "core-js": "^3.19.3", "filepond": "^4.30.4", "filepond-plugin-file-validate-size": "^2.2.7", "filepond-plugin-file-validate-type": "^1.2.8", "nuxt": "^2.15.8", "nuxtjs-mdi-font": "^1.0.2", "vue": "^2.6.14", "vue-bpmn": "^0.3.0", "vue-filepond": "^7.0.3", "vue-print-nb": "^1.7.5", "vue-server-renderer": "^2.6.14", "vue-template-compiler": "^2.6.14", "vue2-editor": "^2.10.3", "vuetify": "^2.6.1", "webpack": "^4.46.0"}, "devDependencies": {"@nuxt/types": "^2.15.8", "@nuxt/typescript-build": "^2.1.0", "@nuxtjs/vuetify": "^1.12.3", "eslint": "8.22.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.25.2", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.4.0", "prettier": "^2.7.1", "typeface-roboto": "^1.1.13"}}